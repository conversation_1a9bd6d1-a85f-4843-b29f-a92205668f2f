<?php

/**
 * Created by Php<PERSON>torm.
 * User: Jason.z<http://www.jason-z.com>
 * Date: 2017/6/7
 * Time: 下午2:42
 */

defined('BASEPATH') or exit ('No direct script access allowed');

require_once APPPATH . 'libraries/alipay/aop/AopClient.php';
require_once APPPATH . 'libraries/alipay/aop/request/AlipayTradeWapPayRequest.php';
require_once APPPATH . 'libraries/alipay/aop/request/AlipayTradeQueryRequest.php';

class Alipaynow extends CI_Controller
{
    //private $appId = '2021004135693334';
    //private $rsaPrivateKey = 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCje5yRNiiH2DIJKWuZ2wj7qiP7vkNmX1uQsX7OyEb5mHEi533/p2bo6LnPBn+tlZlIIIKRqbqXl6FtBYdhCaHOIWrFN7fctRNWJEgtYgzQ1ujgBL5DCs0By9uX0IQ8r+CpRtJaLt6PmKl1PqVgWyNhUjbLb14yS8YyKNeGBHb8zPWpI9L9K/EdIV6cWaPnpp/OGePNVoGBzSsW1Ecw2WCjiKERSx+ddR72NMjEPrXCo5UtTcOo5dp2CHfEh7q3KEUmxZDxvQSBjDl6Q3zarrEpnwm/Tr2wAOsVXS3B93bn4AEQURhFCOrycl72U6wHrbWsqgfuoUqUvd6eq0IT5pU/AgMBAAECggEALJHHpoMYW6iiQ4MqVaC0ez/wXXKKl39JzFHHm4JYbzWTKovn+undkYPZHErORjmxt0s9rY6TUz7AgKNwPBFPLo5HuIqvMpmfMu3jS5QeoFTm2iQQ5uAr8eBs9p+fXZgpbJzlPjbm8EkTceZpWbKfG6o5TA8JpDaqSwR9dYkEE3kwssGd+UsN8vzmzbsPl3mXV2pYzK64bhIVbq5Y9Hy5UUtQ8wAw3lwQevKGlorubU8XOqaglnDamBjsWm0Z1ERKTcKE2bStulhEwf6GrmxG5ex8L7PauXEHzrv7FN/w9AghscbWiT5utnkaAyWbrSnQ5VANMrMOWc+ddXf9P+dDcQKBgQDqMbsRoGGQ1idgaXJDwz83ghb3c0BJEPSErgHkjjrgvqNUXX5actFhRVW/bO4iX6N12b7mw5BYcyskoCyN/pvkz9UAnKrXC1hK+ouOpr0amrvy3ahsrsQ6+yn9dY2vP5CAJuVuY1wet8uL+AcJSf2YePR0cxdh3xSYZwl+IHQK9wKBgQCytGY8yTJLVDfDrIIdQ+xRQdt16lz8PLadFdKE+2aNGreeQNf2WNWXOEWIL8pZwXVKL3e4NwbGRVqTEabHxjOIs3zFDnRgSHtiZSLD745rNnotcgwu21QVBLmlZ2+HJxzqPSkelSnW8qfZTvjRo9yWbhyAdVwSnwX5IYgURpmt+QKBgQDV+qpZ6Jg8pe9EgBQPJg5UwWAKqBtMp1lDAVppYisZvhWtt98C9XWp0pgOV5EdhxTrURDD+Fui9VeuF0ueUcxOvDZ6YKsX4R3DJDjvFhoifM7NkQmag/R3j5VGKGpUfVeiP+fRfKdmybgJI44KRriW36072Qy8N/+xYDOkMjLU8QKBgEt6JjRGRhfK2kKq+cmiho1LY4XKFrvyaK6wKNpB1G6Eekk82foSSyQzgCqlBLIkI4XRxp7nntTfNgIn0mlOLTXvmSMhl8WS14oXbTeUVnKx76iCIuMnf7vg4wgZR+Gtg2jrrBJere07T+lYkX751pEysJcQ+nJi+ALhRrhJmV8BAoGALXGQR/2df0CzUH1t67DUmJX+8I6JA2S3TpVzafU2JUtWQ7PYHqtyu1gl0gP/2urMB5jWFkApM6DpyLvXNZmX4xbesuNXpXhdyoHbVaEPgfuj+OS2D+ZKlumgqJ9sAWiBgIUdKj5t4SXBuncaMbY4WLI9u5Sg5/AOYwHBNDwOZIE='; //私钥
    //private $alipayrsaPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwBziaNpDbhCeQdY1iOCT4UI6oEb+79yJCEMIXyXLetdagCV6MObZ3znt1SzbNBwCJp8ySZUNH1rwfccKHzRHCtn91kTAfYPMe+MFzgTSDoRSIZcNfJNvz+cXoQ9eMPv1Ozp15eSBYqVUOyFF8i4RX19FLzjb5oPm3lG9l+ojBA3LLaK9raR6sYiDZDsJ5w/bQjtwQYGzJo1Wl4Fw8/C9gwj09yvOXykPMJ0P2MPl08EpA0dpiH424K6qVR4qzYsn9SrJ4YiFXGLo0e5Dp+uFjNMmcBmYK3kjr1YJGWdwMV3TgbAck9jP6A+IZMPL3dBcdQ0ZzqoDRdcf/iTTAHMynQIDAQAB'; //公钥

//qj
    private $appId = '2021005161626631';
    private $rsaPrivateKey = 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDLISeoxlzqSSJwQKHX6F/4AQ4YEUUYc77cpT9wdpZ3Vd7P9Cee/rZwclAT4bImxMGMMfNI2QsCETRHsuUO8juII3zS8gT82mJz1enrK7yUKiKxi2em7zahiFw2SilpFVbBq65rWOj5eG3NgRLTiBRC80eLMtwiPiyYNDQoHRXF4qTtDTuGfA0y6Kk1OWXO2PALbYhxNgJDkvBPT/Ci8nrbK8P5mH2pFutZJncgsVb5AFktfAAP18m5UyH7uQzVsn+R9LhUWXnSK1DNbsN5rn8H8jGoAPamI6WteIYzF86YBlAaQ6/BFzsyjn3a/6VEGCWAOL2oggWnEARH3xD102OTAgMBAAECggEAf8ToFFJJiZjdD6yn4I4rJ21KdzsNsjbD/Df5Mn11YbTixP13mpvvF8XfttHFZHtonw2gUNctBGzb7KmLSR528cgYsPtIxd98BA6u550ILk5eqjzcwvU+8NY74m49o/qnKoUZ3i37xUfKEXS56dJzpDUwcE9ahjODi21yzTSWSHSKShDa6gi2upxsjylM94qXr5mNUHoXp1z9HhOW/PC373w2JoFIRFQ6NJ+4RH3TJo5MsI1iqdCiLcXch91jBSU68vye5Ia9BUN5pmbjAkSdiby7uVzlHj6EyNH1AcUqomH3viTZOigDm29wjLOW0SzVkkEcm70dPRfT4Pf9AG9e6QKBgQD6ymVpgdbQAIFKlxmTPemctUA3CPAiyPD2Id52eQ4bIwe0TdLV8gpaRjthjX6In1cHEF+K1KnYKSZ0d6WjcEW4wAYomnRMkSTYtQjO5wqzfrTYio93VRlX/7A2xVjn6Gm7K3WBZbA+yLS8o/PMx6V1e9EQ6b/5VOP/lWHvjGdzNwKBgQDPWVDw27qwtzM9oc7KwTo/0SeXwWRVM5jp6BWN9U57GszazLuVb1GunDS8rpQNeUsBjPlDiVgvVsfBUluqz3LmNGVmLfPMWFD9vOt23DttzFtjy7ueT3ip1Cs56Yy4hdBS4ZlX0NjAMOwOWzgqAgYUi9GYdSUX84XkNpwrPWO4hQKBgByxT0z05+jARgqMowZvjOy65+/j++Ve7AKc+WDAlNaR+W2tgbOsj3aoll6g25sx77kFLIkrr4rlmlq03aSHUdLDVXELysZ/zKFmhO13DYhftL0ZjTRv7ghdXoHFrlhKBN6/KCB3y+RC/jqe32PMa6e3VMIEnEwgZD13j87iV2udAoGAdl5xs55snFdpXCTULbFTdH0NUjfn9C/JerawH2NL00FAcyPvbmanRbwM6y3qfSqiJBd7cQ+mFRhAXt+KTx+wYzTEMinHhP9h9Qh1j1w4XP0NqxHyYHTT2Hez2ZK3b9uMStRZOuWR513GVIfEr06KdA5Br/woGd0+sUJvssuP8xUCgYEA9gqtwhMMBgAHetuc7KklNQNjWnyq2rJc8iW8E0+RGuNWRLhvydWlOiQXsnqra5IrOqMWpHEQEEj8v9Aodi759NT9tk9MiQs7gN7n5a3P+FtZhFG7dVbwCJ83ou6rHIriMXtz9clii9njwkUvPdBWtCa433VPLNXK1wsDVMCjrlo='; //私钥
   private $alipayrsaPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApJVZmcMJFlN9uop5cfNqiSOKIvhO9RBSDFO/LmJJ8E7+vTSZO5dFkktpoB9biNPPFj3wwCcuQb6nKpHSUU6nE3h+RAyrvENsHKJ6K3RI30YieqIQ2ABZSwHgk/wG1h8wR84IX4ILPGtcCyO6ni8rfq480SrYLkgzOQwPYYiiayGDsS2EPLWYvNKdwKjOzcMMeEx7vte8OXFuM0qBPC7puh0yqwtYooQtl+kv6UJnss3Bh60WiE2C5+wRBjr0mygbkSiLw5EVgila2bfI7qvrwsswvdfkjhsKOwd5NJ+xbFFZkijBlb3LrWUrNEuIgUPKPkVKDNO9ocT45GoKdH2MhwIDAQAB'; //公钥

    //private $appId = '2021004141632174';
   // private $rsaPrivateKey = 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCHWg8Eir+VIpvtlLuTjkdEfuwzJvqLCk0OhHNVCmH/lpjvmDh/coX8P/qFP9wCxvwCQiUKu+Q9nHf40i2gHaJ0xSraTp3OgCM3MKcZYjOn3Rt6DofsQu4XMkmBqYGvlEwJNn6sBB5bVwtPDdGipsIv0JLyX424d1cHt3FHE+lkOqUzNKfw7usbAdYMHy7zJdClW1gMXOyQ23EJ1Tlh0oXChu5zsiPUwYphIk0nq292NrsXbL/j0HURfjivLPOJts9FIct8riUeKm5aAeuIMXtEgp9YB1v72PKCHGl5Fopev0k5hUR373+naDsU5yYr3SZmIDSI6MKavdbcqraxM855AgMBAAECggEACsU40rSzwAtL+yDNuFUnCKVM6FkesLe2IAScJNU5rRvvuYkUXdfgH417wuSs/i+VWBypU+xVoi10QoF/DfGtvrr9yPdeiVMa+kZN1bBBygbHK/RCDqPYzFG9FatGXZFgZ49FjyyYuc34YWcJGndIloNzXgQKg/ZDH6MSSYSlGsuN+7PbR1M+pnyVnRQpbmWTKYvYKxNpYw3Iy8OVgeeMpI6+h8WkR8fVnbjExopv4ZsCNDLHgvBoP3gQ8meUotdYOK5m3n0ELXq2xAr0k966UcZqk6P9iwn4u8kmba+9Fdiot0goywwwfdPmyV5aI8cq2JBhvuh98BSpUa9FLCaacQKBgQDU0IlWFjTAi5C2MUe+r+8b0NKXBQIjPwxb+P86bVNC4+D+vjGZ801ZmVRMMgD1sHuEGA5quLlmJUtfRPgXIB9MSTgEy1XA5u/6d9RAesizd79GiRkzdkN9nYLiXUFNYBKR5ZhI3cOrwegNR7ShyTyXzV6O528ekBVSj/v7ODl+ZQKBgQCi0WwYBLjK/zTWiDxKrpJHgLCHvt7c1Y5Kvg2NMQVxsg5kz3Y6MC/v6AmS1mK8VdA7/pV5XGEsevvMxp2Q8RQx78HwFI9jQjrIxUdPtL1A7V8KXG0xKPn0C7rAKInyWb9kOQft2NviU+6ZcNqSz6OedzekY6kE9vGwaeknyqxUhQKBgQCqZVaxYRJX4Oe7mXWyjrfK88bKOK5ffkntEk95frOd6UvrbGnWtGKIjSZb4+W00tvzBjf053c6/D1/SH/ENGoa8L0zyYMd80rOp0eBYU51tDmoxnkxJU4gtA9qI/T3eSclPkpSfY4AZj/g8jsFF1W2zi8aCOCF/iO0eajumzQdqQKBgDU/WiOMv8abqfMTQOyHpQ0/V/w8/vOn384v04y+YHJrceukMZ0cD0FdWGwuMsI3Kk/ElavH+3upiNnShstVHG6sY3eZsb8bdNAePJEjodUvDCr+STOPnBclEuEvINYiZcZoJGCz0E13d4u07bafs3nqsUbQ1QDrpeU07ex2it9lAoGATnQ1PeFQl2DOfS93YCcBMroGgLm+6rSlkiWuFWjK15GYLeD5AsHLr590+QOZhQLd39esYkM6fgb5V2VsDTSpZCMLFcqcLwYl+kZVGEudTfu1ghi57tMQYr7LVoWRmI+i3yLhN3W3dab7XGBvX4NlzQT/G7CX/CJywNBMj3FaAD4='; //私钥
    //private $alipayrsaPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr+TTr5Z7ka7Zj2mGsVLl9jMQpWHl6yvnCXVgGKvXbO2QYkHccny8EE//2dqEkWx2esqws3tIriHiLJMEYnaCYrBw0ZDFqPAnKn4XvH14j40dFrQc8Rw4o0y2FZQuaqDXPPUa4oPdEq1h6k/EVGFvOMH6ih7Sf9I/Lgs5Z7se2vjkiOSwH/9OCHNmecW9ZoiVN4mpcodtri/Y8LSujZp56Ci/lvMhHWxBNHzB3HSlCP+kZ7SijDwY5oOQG20XyL5B8ZSlbK4Bo0PmIpIP5bQxqp6HiouU4ZFg3F1PDm+I3dU8W1oyuLVuz2Z7f1YfCa6UbFLJRL3ylI5kwL9zaDbYlQIDAQAB'; //公钥

   
    private $gatewayUrl = 'https://openapi.alipay.com/gateway.do'; //网关地址
    private $returnUrl = 'https://lhmj.tuo3.com.cn/admin/api/v1/Alipaynow/returnPay'; //同步跳转地址
    private $notifyUrl = 'https://lhmj.tuo3.com.cn/admin/api/v1/Alipaynow/notifyPay'; //异步通知地址
    private $quitUrl = ''; //支付失败退出地址
    private $version = '1.0';
    private $signType = 'RSA2';
    private $charset = 'utf-8';
    private $format = 'json';

    function __construct()
    {
        parent::__construct();
        $this->load->model('mp_model');
        $this->load->model('server_model');
        $this->load->model('player_model');
        $this->load->model('user_model');
    }

    public function index(){
        $this->load->view('alipay/index');
    }
      
    public function pay(){
        $good_id = $this->input->get('good_id');
        $role_id = $this->input->get('user_id');
        $game_id = $this->input->get('game_id');
        $this->player_model->set_database($game_id);

        // 查询角色是否存在
        $role = $this->player_model->get_role_info($role_id);

        if (empty ($role)) {
            echo json_encode(array('status' => false, 'msg' => '角色不存在'));
            exit;
        }

        // 获取商品信息
        $good = $this->mp_model->get_mp_good_by_id($good_id);

        if (empty ($good)) {
            echo json_encode(array('status' => false, 'msg' => '请选择充值商品'));
            exit;
        }
        //获取用户微信信息
        $mp_user = $this->mp_model->get_mp_user_by_roleid($role_id, $game_id);

        $agent = $this->user_model->get_one_agent($role_id, $game_id);
        
        $order_no = date("YmdHis") . random_string('nozero', 3);

        $price =  $good['price'];
        // 创建订单
        $data = array(
            'user_id' => $mp_user['user_id'],
            'role_id' => $role_id,
            'agent_id' => $agent ? $agent['id'] : 0,
            'good_id' => $good_id,
            'game_id' =>$game_id,
            'order_no' => $order_no,
            'is_first' => $good['is_first'],
            'is_agent' => ($good['type'] - 1),
            'total_fee' => $price,
            'is_mall' => 1,
            'kind_id' => $role['CommonKindID'],
            'create_time' => time()
        );

        $this->db->insert('mp_order', $data);
        $aop = new AopClient();
        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = $this->appId;
        $aop->rsaPrivateKey = $this->rsaPrivateKey;
        $aop->alipayrsaPublicKey = $this->alipayrsaPublicKey;
        $aop->apiVersion = '1.0';
        $aop->signType = 'RSA2';
        $aop->postCharset = 'UTF-8';
        $aop->format = 'json';
        /******必传参数******/
        $object = new stdClass();
        //商户订单号，商家自定义，保持唯一性
        $object->out_trade_no = $order_no;        
        //支付金额，最小值0.01元
        // $object->total_amount = $good['price'];
        $object->total_amount = $price;
        //订单标题，不可使用特殊符号
        $object->subject = $good['name'];
        /******可选参数******/
        //手机网站支付默认传值QUICK_WAP_WAY
        $object->product_code = 'QUICK_WAP_WAY';
        $object->time_expire = date("Y-m-d H:i:s", strtotime("+1 hours"));
        $json = json_encode($object);
        $request = new AlipayTradeWapPayRequest();
        //异步接收地址，仅支持http/https，公网可访问
        $request->setNotifyUrl($this->notifyUrl);
        //同步跳转地址，仅支持http/https
        $request->setReturnUrl($this->returnUrl);
        $request->setBizContent($json);
        $pageRedirectionData = $aop->pageExecute($request, "POST");
        echo json_encode(array('status' => true, 'msg' => '', 'data'=>$pageRedirectionData));
        exit; 
    }
    public function returnPay()
    {
        $out_trade_no = $this->input->get('out_trade_no');
        $trade_no = $this->input->get('trade_no');        
        log_message("error", "支付宝-现在支付页面返回");
        log_message("error", print_r($this->input->get(),true));
        $data = $this->payCheck($out_trade_no,$trade_no);
        $this->load->view('alipay/returnPay',$data);
        // $this->_render_page('alipay/returnPay', $this->data);
        // exit;
    }


    public function notifyPay()
    {
        $content = file_get_contents('php://input');
        parse_str($content,$result);
        $out_trade_no = $result['out_trade_no'];
        $trade_no = $result['trade_no'];        
        log_message("error", "支付宝-现在支付回调");
        log_message("error", print_r($result,true));
        $this->payCheck($out_trade_no,$trade_no);
        exit;
    }
    private function payCheck($out_trade_no,$trade_no){
        // 查找订单
        $this->db->where('order_no', $out_trade_no);
        $query = $this->db->get('mp_order');
        $order = $query->row_array();
        if(empty($order)){
            log_message("error", "支付宝-订单未找到");
            // echo "订单未找到";
            return ["code"=>-1,"msg"=>"交易失败,订单未找到"];
            // exit;
        }
        if ($order['status'] == 1) {
            log_message("error", "支付宝-交易已完成");
            // echo "交易已完成,请退出游戏重新登陆";
            return ["code"=>1,"msg"=>"交易已完成,请退出游戏重新登录"];
            // exit;
        }
        log_message('error', '订单信息');
        log_message('error', print_r($order,true));       
    
        $aop = new AopClient();

        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = $this->appId;
        $aop->rsaPrivateKey = $this->rsaPrivateKey;
        $aop->alipayrsaPublicKey = $this->alipayrsaPublicKey;
        $aop->apiVersion = '1.0';
        $aop->signType = 'RSA2';
        $aop->postCharset = 'UTF-8';
        $aop->format = 'json';
       
        $object = new stdClass();
        $object->out_trade_no = $out_trade_no;
        //$object->trade_no = '2014112611001004680073956707';
        $json = json_encode($object);
        $request = new AlipayTradeQueryRequest();
        $request->setBizContent($json);
        $result = $aop->execute($request); 
        if(empty($result->alipay_trade_query_response)){
            log_message('error', '获取支付宝支付信息失败');
            return ["code"=>-1,"msg"=>"交易失败，获取支付宝支付信息失败"];
            // exit;
        }
        $result = $result->alipay_trade_query_response;
        // dump($paymentInfo);
        if ($result->code === "10000" && $result->trade_status == "TRADE_SUCCESS") {
            $this->db->where('id', $order['id']);
            $this->db->update('mp_order', array('transaction_id' => $trade_no, 'pay_time' => time(), 'status' => 1));

            $this->player_model->set_database($order['game_id']);

            // 获取商品信息
            $good = $this->mp_model->get_mp_good_by_id($order['good_id']);
            $role = $this->player_model->get_role_info($order['role_id']);

            //添加房卡
            $this->player_model->update_role_score($order['role_id'], $good['score']);
            $adminer = array();
            if ($order['game_id'] == 30) {
                // 查询是否为代理
                $adminer = $this->user_model->get_one_agent($order['role_id'], $order['game_id']);
            }

            // 获取推荐代理
            if ($role && $role['SpreaderID'] > 0) {

                $agent = $this->user_model->get_one_agent($role['SpreaderID'], $order['game_id']);

                if ($agent) {

                    $rate = 0.4;

                    if ($order['game_id'] == 20) {
                        $rate = 0.5;
                    }


                    // 插入到收益表
                    $data = array(
                        'a_id' => $role['UserID'],
                        'a_account' => $role['Accounts'],
                        'b_id' => $agent['id'],
                        'b_account' => $agent['username'],
                        'create_time' => time(),
                        'type' => 4,
                        'money' => $rate * $order['total_fee'],
                        'district_id' => empty ($agent['district_id']) ? 0 : $agent['district_id']
                    );

                    $this->db->insert('bonus', $data);

                    $this->user_model->update_agent_bonus($agent['id'], $rate * $order['total_fee']);

                    // 获取上级代理
                    $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);

                    // 判断其下级代理个数
                    $count = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                    $rate = 0.06;



                    // 插入到收益表
                    $data = array(
                        'a_id' => $agent['id'],
                        'a_account' => $agent['username'],
                        'b_id' => $parent['id'],
                        'b_account' => $parent['username'],
                        'create_time' => time(),
                        'type' => 1,
                        'money' => $rate * $order['total_fee'],
                        'district_id' => empty ($agent['district_id']) ? 0 : $agent['district_id']
                    );

                    $this->db->insert('bonus', $data);

                    $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

                    if ($parent['spreader_id'] > 0) {
                        $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                        // 判断其下级代理个数
                        $count2 = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                        $rate = 0.04;



                        // 插入到收益表
                        $data = array(
                            'a_id' => $agent['id'],
                            'a_account' => $agent['username'],
                            'b_id' => $parent2['id'],
                            'b_account' => $parent2['username'],
                            'create_time' => time(),
                            'type' => 2,
                            'money' => $rate * $order['total_fee'],
                            'district_id' => empty ($agent['district_id']) ? 0 : $agent['district_id']
                        );

                        $this->db->insert('bonus', $data);

                        $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);
                    }
                }                
            } else if ($adminer) {
                $parent = $this->user_model->get_agent_by_id($adminer['spreader_id']);

                if (in_array($order['game_id'], array(30))) {
                    $rate = 0.376;
                }

                // 插入到收益表
                $data = array(
                    'a_id' => $adminer['id'],
                    'a_account' => $adminer['username'],
                    'b_id' => $parent['id'],
                    'b_account' => $parent['username'],
                    'create_time' => time(),
                    'type' => 1,
                    'money' => $rate * $order['total_fee'],
                    'district_id' => empty ($adminer['district_id']) ? 0 : $adminer['district_id']
                );

                $this->db->insert('bonus', $data);

                // 更新收益
                $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

                // 获取上上级代理
                if ($parent['spreader_id'] > 0) {
                    $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);

                    if (in_array($order['game_id'], array(30))) {
                        $rate = 0.047;
                    }

                    // 插入到收益表
                    $data = array(
                        'a_id' => $adminer['id'],
                        'a_account' => $adminer['username'],
                        'b_id' => $parent2['id'],
                        'b_account' => $parent2['username'],
                        'create_time' => time(),
                        'type' => 2,
                        'money' => $rate * $order['total_fee'],
                        'district_id' => empty ($adminer['district_id']) ? 0 : $adminer['district_id']
                    );

                    $this->db->insert('bonus', $data);

                    $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);

                    if ($parent2['spreader_id'] > 0) {
                        // 获取上上级代理
                        $parent3 = $this->user_model->get_agent_by_id($parent2['spreader_id']);

                        if (in_array($order['game_id'], array(30))) {
                            $rate = 0.0188;
                        }

                        // 插入到收益表
                        $data = array(
                            'a_id' => $adminer['id'],
                            'a_account' => $adminer['username'],
                            'b_id' => $parent3['id'],
                            'b_account' => $parent3['username'],
                            'create_time' => time(),
                            'type' => 3,
                            'money' => $rate * $order['total_fee'],
                            'district_id' => empty ($adminer['district_id']) ? 0 : $adminer['district_id']
                        );

                        $this->db->insert('bonus', $data);

                        $this->user_model->update_agent_bonus($parent3['id'], $rate * $order['total_fee']);
                    }
                }
            }
            log_message("error", "支付宝-支付成功");
            // echo "支付成功";
            return ["code"=>1,"msg"=>"支付成功，请退出游戏重新登录"];
            // exit;
        } else {
            log_message("error", "支付宝-交易已完成");
            // echo "订单状态异常";
            return ["code"=>-1,"msg"=>"交易失败，订单状态异常"];
            // exit;
        }
    }



    public function goods()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');
       
        $role_id = $this->input->get('user_id');

        $game_id = $this->input->get('game_id');

        $this->player_model->set_database($game_id);

        $role = $this->player_model->get_role_info($role_id);

        if (!$role) {
            echo json_encode(array('status' => false, 'msg' => '未绑定游戏角色，请下载并登录游戏', 'is_bind' => false, 'is_agent' => false));
            exit;
        }

        $adminer = $this->user_model->get_one_agent($role['UserID'], $game_id);

        $image = ['0' => 'https://file.tuo3.com.cn/a.png', '1' => 'https://file.tuo3.com.cn/b.png', '2' => 'https://file.tuo3.com.cn/c.png', '3' => 'https://file.tuo3.com.cn/d.png', '4' => 'https://file.tuo3.com.cn/d.png', '5' => 'https://file.tuo3.com.cn/d.png', '6' => 'https://file.tuo3.com.cn/d.png', '7' => 'https://file.tuo3.com.cn/d.png', '8' => 'https://file.tuo3.com.cn/d.png', '9' => 'https://file.tuo3.com.cn/d.png', '10' => 'https://file.tuo3.com.cn/d.png', '11' => 'https://file.tuo3.com.cn/d.png', '12' => 'https://file.tuo3.com.cn/d.png', '13' => 'https://file.tuo3.com.cn/d.png', '14' => 'https://file.tuo3.com.cn/d.png', '15' => 'https://file.tuo3.com.cn/d.png'];

        $result = $this->mp_model->get_role_is_first_order($role['UserID'], $game_id);

        $goods_id = array();
        if (!empty ($result)) {
            foreach ($result as $key => $res) {
                $goods_id[$key] = $res['good_id'];
            }
        }

        // 是否绑定
        if ($role['SpreaderID'] <= 0 && !$adminer) {
            //首充
            if (in_array($game_id, array(6, 7, 17))) {
                // 判断是否首充
                //                $this->db->join('mp_good', 'mp_good.id=mp_order.good_id');
                $this->db->where('role_id', $role['UserID']);
                //                $this->db->where('mp_good.game_id', $game_id);
                $this->db->where('mp_order.game_id', $game_id);
                $this->db->where('mp_order.is_first', 1);
                $this->db->where('mp_order.status', 1);
                $query = $this->db->get('mp_order');
                $result = $query->result_array();

                // 获取计费ID
                $this->db->where('type', 1);
                $this->db->order_by('price', 'ASC');
                if ($result) { // 剔除首充商品
                    $this->db->where('is_first', 0);
                }
                $this->db->where('game_id', $game_id);
                $query = $this->db->get('mp_good');
                $goods = $query->result_array();
            } else {
                // 获取商品信息
                $goods = $this->mp_model->get_mp_good_by_type(1, $game_id, $sort = 'ASC', $goods_id);
            }

            if ($game_id != 30) {
                foreach ($goods as $key => $value) {
                    $goods[$key]['price'] = ($value['price'] * 1.25);
                }
            }

            if (!empty ($goods)) {
                foreach ($goods as $key => $value) {
                    for ($i = 0; $i <= count($image) - 1; $i++) {
                        if ($i == $key) {
                            $value['icon'] = $image[$i];
                        }
                    }
                    $goods[$key] = $value;
                }
            }

            echo json_encode(array('status' => true, 'msg' => '', 'role' => array('is_bind' => false, 'is_agent' => true), 'goods' => $goods, 'game_id' => $game_id));
            exit;           
        } else {
            //首充
            if (in_array($game_id, array(6, 7, 17))) {
                // 判断是否首充
                $this->db->join('mp_good', 'mp_good.id=mp_order.good_id');
                $this->db->where('role_id', $role['UserID']);
                $this->db->where('mp_good.game_id', $game_id);
                $this->db->where('mp_order.game_id', $game_id);
                $this->db->where('mp_good.is_first', 1);
                $this->db->where('mp_order.status', 1);
                $query = $this->db->get('mp_order');
                $result = $query->result_array();

                // 获取计费ID
                $this->db->where('type', 1);
                $this->db->order_by('price', 'ASC');
                if ($result) { // 剔除首充商品
                    $this->db->where('is_first', 0);
                }
                $this->db->where('game_id', $game_id);
                $query = $this->db->get('mp_good');
                $goods = $query->result_array();
            } else {
                // 获取商品信息
                $goods = $this->mp_model->get_mp_good_by_type(1, $game_id, $sort = 'ASC', $goods_id);
            }

            if (!empty ($goods)) {
                foreach ($goods as $key => $value) {
                    for ($i = 0; $i <= count($image) - 1; $i++) {
                        if ($i == $key) {
                            $value['icon'] = $image[$i];
                        }
                    }
                    $goods[$key] = $value;
                }
            }

            echo json_encode(array('status' => true, 'msg' => '', 'role' => array('is_bind' => true, 'is_agent' => true), 'goods' => $goods, 'game_id' => $game_id));
            exit;
        }        
    }

    private function getOptions()
    {
        $options = new Config();
        $options->protocol = 'https';

        $options->gatewayHost = 'openapi.alipay.com';

        $options->signType = 'RSA2';

        $options->appId = '2021004135693334';

        // 为避免私钥随源码泄露，推荐从文件中读取私钥字符串而不是写入源码中

        $options->merchantPrivateKey = $this->rsaPrivateKey;

        // $options->alipayCertPath = '';

        // $options->alipayRootCertPath = '';

        // $options->merchantCertPath = '<-- 请填写您的应用公钥证书文件路径，例如：/foo/appCertPublicKey_2019051064521003.crt -->';
        // $options->merchantCertPath ='';

        //注：如果采用非证书模式，则无需赋值上面的三个证书路径，改为赋值如下的支付宝公钥字符串即可

        // $options->alipayPublicKey = '<-- 请填写您的支付宝公钥，例如：MIIBIjANBg... -->';
        $options->alipayPublicKey = $this->alipayrsaPublicKey;

        //可设置异步通知接收服务地址（可选）

        //如果需要使用文件上传接口，请不要设置该参数

        $options->notifyUrl = $this->notifyUrl;

        //可设置AES密钥，调用AES加解密相关接口时需要（可选）

        $options->encryptKey = "qy8SyQmK4zKWvAemodhEtA==";

        return $options;
    }
    private function _response($arr)
    {
        echo json_encode($arr);
        exit;
    }
}
