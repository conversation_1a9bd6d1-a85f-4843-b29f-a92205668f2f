<?php
/**
 * 简化的阿里云短信测试脚本
 */

require_once('AliyunSms.class.php');

// 直接在这里填写您的配置信息进行测试
$config = array(
    'accessKeyId' => 'YOUR_ACCESS_KEY_ID',        // 替换为您的AccessKey ID
    'accessKeySecret' => 'YOUR_ACCESS_KEY_SECRET', // 替换为您的AccessKey Secret
    'signName' => 'YOUR_SIGN_NAME',               // 替换为您的短信签名
    'templateCode' => 'SMS_YOUR_TEMPLATE_CODE'    // 替换为您的模板CODE
);

// 测试手机号（替换为您的手机号）
$testPhone = '13800138000';

echo "=== 阿里云短信服务测试 ===\n";

// 检查配置
if ($config['accessKeyId'] == 'YOUR_ACCESS_KEY_ID') {
    echo "❌ 请先修改配置信息！\n";
    echo "需要修改的配置项：\n";
    echo "- accessKeyId: 您的阿里云AccessKey ID\n";
    echo "- accessKeySecret: 您的阿里云AccessKey Secret\n";
    echo "- signName: 您申请的短信签名\n";
    echo "- templateCode: 您申请的短信模板CODE\n";
    exit;
}

// 初始化短信服务
$sms = new AliyunSms($config);

// 生成验证码
$code = rand(1000, 9999);

echo "配置信息：\n";
echo "- 手机号: $testPhone\n";
echo "- 签名: " . $config['signName'] . "\n";
echo "- 模板: " . $config['templateCode'] . "\n";
echo "- 验证码: $code\n";
echo "\n正在发送短信...\n";

// 发送短信
$result = $sms->templateSMS('', $testPhone, $config['templateCode'], $code);
$response = json_decode($result);

echo "\n=== 发送结果 ===\n";
echo "原始响应: $result\n";

if ($response && $response->resp->respCode == "000000") {
    echo "✅ 发送成功！\n";
    echo "请检查手机 $testPhone 是否收到验证码: $code\n";
} else {
    echo "❌ 发送失败\n";
    if ($response) {
        echo "错误码: " . $response->resp->respCode . "\n";
        echo "错误信息: " . $response->resp->respMsg . "\n";
    }
    
    echo "\n🔍 故障排查建议：\n";
    echo "1. 确认AccessKey ID和Secret正确\n";
    echo "2. 确认短信签名已审核通过\n";
    echo "3. 确认短信模板已审核通过\n";
    echo "4. 确认账户有足够余额\n";
    echo "5. 确认手机号格式正确\n";
    echo "6. 检查模板变量是否匹配（需要包含\${code}）\n";
}

echo "\n测试完成。\n";
?>
