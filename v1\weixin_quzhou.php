<?php

defined('BASEPATH') OR exit('No direct script access allowed');

// This can be removed if you use __autoload() in config.php OR use Modular Extensions
/** @noinspection PhpIncludeInspection */
require(APPPATH . 'libraries/REST_Controller.php');
require_once(APPPATH . 'libraries/wxpay/WxPay.JsApiPay.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Api.php');

class Weixin_quzhou extends CI_Controller {

    function __construct()
    {
        // Construct the parent class
        parent::__construct();
        $this->load->library('CI_Wechat',array('name'=>'quzhou'));
    }


     public function index(){

//        log_message("error", var_export($_GET,true));
//
//        $return = $this->ci_wechat->valid(true);
//
//        echo $return;
//
//        log_message("error", $return);
//
//         exit;
        $text = "您好，欢迎关注多多衢州麻将公众号！如有问题，请直接回复公众号，我们将有人工客服为你解答！\n\n<a href='https://lhmj.tuo3.com.cn/wap/#/tip'>1.点击：申请会员</a>\n\n<a href='https://lhmj.tuo3.com.cn/wap/#/tip'>2.点击：房卡购买</a>\n\n<a href='http://a.mlinks.cc/AKKX'>3.点击：下载游戏</a>\n\n<a href='https://lhmj.tuo3.com.cn/wap/#/tip'>4.点击：会员后台</a>";

        $type = $this->ci_wechat->getRev()->getRevType();

        switch($type) {
        	case Wechat::MSGTYPE_TEXT:
                    $content = $this->ci_wechat->getRev()->getRevContent();
                    if(strstr($content,'卡')) {
                         $this->ci_wechat->text('你好，多多衢州麻将现在是免费测试期，如果您的房卡已经消耗完毕，请联系客服微信号duoduomj100，免费索取；另外，多多衢州麻将正在招募房卡销售代理商，如果您想成为我们的房卡销售代理，也可以咨询客服微信号；0成本加入代理，感谢您的询问')->reply();
                    }
        			break;
        	case Wechat::MSGTYPE_EVENT:
        	        $event = $this->ci_wechat->getRev()->getRevEvent();
        	        switch($event['event']) {
                        case Wechat::EVENT_SUBSCRIBE:
                            $news = array(
                                "0"=>array(
                                        'Title'=>'发红包啦，新手玩家猛戳',
                                        'Description'=>'终于等到你，还好我没放弃~~~~废话不说，进入正题，请看以下内容。',
                                        'PicUrl'=>'http://mmbiz.qpic.cn/mmbiz_jpg/PeZD6Wic6gplzpU1blTia7XOpCfO9sCKJbwXuxupvxTDEFz7zXDw6xFQY0zb8stpEdicyjFGLFdEy3ibI1uXIqibtCg/640?wx_fmt=jpeg&tp=webp&wxfrom=5&wx_lazy=1',
                                        'Url'=>'http://mp.weixin.qq.com/s/i7WpWzvY5y4z7rPNTtJbeQ'
                                    )
                            );
                            $this->ci_wechat->news($news)->reply();
                            $this->ci_wechat->text($text)->reply();
                            break;
                        case Wechat::EVENT_MENU_CLICK:
                            $this->parse_menu_click($event['key']);
                            break;
                        default:
                            break;
        	        }
        			break;
        	case Wechat::MSGTYPE_IMAGE:
        			break;
        	default:
        			break;
        }
     }

    // 解析菜单点击事件
     private function parse_menu_click($key) {

        switch($key) {
            case 'menu_customer':
                $this->ci_wechat->text("小白娱乐欢迎您!\n\n客服工作时间：9:00-21:00\n\n请详细描述您要咨询的问题，客服接入后会帮您解答!\n\n当前人工客服接受咨询人数较多，请您耐心等待")->reply();
                break;
            case 'menu_notice':
                $news = array(
                    "0"=>array(
                	   		'Title'=>'官方声明',
                	  		'Description'=>'多多游戏全力为广大玩家打造一个健康绿色的棋牌竞技娱乐平台，提倡健康休闲的游戏理念，坚决打击任何形式私下转帐、红包赌博等违法行为，玩家如与公司提倡的绿色游戏理念初衷相违背，有严重损害公司形象的行为，一经查实，将进行扣卡封号处理，情节严重的将报送公安机关。',
                	   		'PicUrl'=>'http://mmbiz.qpic.cn/mmbiz_png/PeZD6Wic6gpkhTfaooBuu1hszaabFH4qUOBEMYficg6bsI02w9Y6HI74FJAqno0F2rsfyRjJc35T7jKhMUI4NKQg/640?wx_fmt=png&tp=webp&wxfrom=5',
                	  		'Url'=>'http://mp.weixin.qq.com/s/8RcpIut8Hkjt0GtJ671GtQ'
                	   	)
                );
                $this->ci_wechat->news($news)->reply();
                break;
            case 'menu_activity':
                $this->ci_wechat->text('当前没有活动，敬请期待！')->reply();
                break;
            case 'menu_download_desc':
               $news = array(
                       "0"=>array(
                            'Title'=>'安卓手机下载说明',
                            'Description'=>'新手玩家必看（安卓手机下载游戏说明）',
                            'PicUrl'=>'http://mmbiz.qpic.cn/mmbiz_jpg/PeZD6Wic6gpkhTfaooBuu1hszaabFH4qUY4kyEKbbMvNV3CDL5RzcZUcU7tFdFEsnJ2BlI8KYTA2aqAu7OOiboRQ/640?wx_fmt=jpeg&tp=webp&wxfrom=5&wx_lazy=1',
                            'Url'=>'http://mp.weixin.qq.com/s/azQ828nQUdy-ZYg9vnl4_w'
                        ),
                        "1"=>array(
                            'Title'=>'苹果手机下载说明',
                            'Description'=>'新手玩家必看（苹果手机下载游戏说明）',
                            'PicUrl'=>'http://mmbiz.qpic.cn/mmbiz_jpg/PeZD6Wic6gpkhTfaooBuu1hszaabFH4qUY4kyEKbbMvNV3CDL5RzcZUcU7tFdFEsnJ2BlI8KYTA2aqAu7OOiboRQ/640?wx_fmt=jpeg&tp=webp&wxfrom=5&wx_lazy=1',
                            'Url'=>'http://mp.weixin.qq.com/s/LKAZpqkki5okqbfqmtpMbw'
                        )
                   );
                $this->ci_wechat->news($news)->reply();
                break;
            case 'menu_ios_install':
               $news = array(
                       "0"=>array(
                            'Title'=>'苹果安装说明',
                            'Description'=>'苹果手机下载安装说明',
                            'PicUrl'=>'http://mmbiz.qpic.cn/mmbiz_jpg/PeZD6Wic6gpkhTfaooBuu1hszaabFH4qUzhGgK2unicbYZZrJrHTyic4xxNzOruic5YqYQ3CzyyibT2iavY68KBVtHlw/640?wx_fmt=jpeg&tp=webp&wxfrom=5&wx_lazy=1',
                            'Url'=>'http://mp.weixin.qq.com/s/NfvDn9dAfGHm9lHvPSCtnA'
                        )
                   );
                $this->ci_wechat->news($news)->reply();
                break;
            case 'menu_card_buy':
                $this->ci_wechat->text('房卡购买请联系群主或客服微信号：duoduomj100')->reply();
                break;
            case 'menu_agent_apply':
                $this->ci_wechat->text('有关代理招募事项，请联系微信号：duoduomj100')->reply();
                break;
            default:
                break;
        }

     }

    public function create_menu()
    {
             //获取菜单操作:
            $menu = $this->ci_wechat->getMenu();
             //设置菜单
            $newmenu =  array(
                "button"=>
                        array(
                            array(
                                'name'=>'客服中心',
                                'sub_button'=>array(
                                    array(
                                        "type"=>"click",
                                        "name"=>"人工客服",
                                        "key"=>"menu_customer"
                                    ),
                                    array(
                                        "type"=>"click",
                                        "name"=>"官方声明",
                                        "key"=>"menu_notice"
                                    ),
                                    array(
                                        "type"=>"click",
                                        "name"=>"最新活动",
                                        "key"=>"menu_activity",
                                    ),
                                )
                            ),
                            array(
                                'name'=>'游戏下载',
                                'sub_button'=>array(
                                    array(
                                        "type"=>"view",
                                        "name"=> "游戏下载地址",
                                        "key"=>"menu_download",
                                        "url"=>"http://a.mlinks.cc/AKKX",
                                    ),
                                    array(
                                        "type"=>"click",
                                        "name"=> "游戏下载说明",
                                        "key"=>"menu_download_desc",
                                    ),
                                    array(
                                        "type"=>"click",
                                        "name"=>"苹果安装说明",
                                        "key"=>"menu_ios_install",
                                    )
                                )
                            ),
                            array(
                                'name'=>'游戏充值',
                                'sub_button'=>array(
//                                    array(
//                                        "type"=>"click",
//                                        "name"=> "申请代理",
//                                        "key"=>"menu_agent_apply",
//                                    ),
                                    array(
                                        "type"=>"view",
                                        "name"=>"会员充值",
                                        "key"=>"menu_card_buy",
                                        "url"=>"https://lhmj.tuo3.com.cn/wap/#/pay?game_id=7",
                                    ),
                                    array(
                                        "type"=>"view",
                                        "name"=>"会员登录",
                                        "key"=>"menu_agent_login",
                                        "url"=>"https://lhmj.tuo3.com.cn/server2/",
                                    )
//                                     array(
//                                        "type"=>"view",
//                                        "name"=>"代理后台",
//                                        "key"=>"menu_agent_admin",
//                                        "url"=>"https://lhmj.tuo3.com.cn/wap/#/tip",
//                                    ),
//                                    array(
//                                        "type"=>"view",
//                                        "name"=>"代理套餐",
//                                        "key"=>"menu_agent_buy",
//                                        "url"=>"https://lhmj.tuo3.com.cn/wap/#/tip",
//                                    )
                                )
                            ),
                        )
                    );

            $result =  $this->ci_wechat->createMenu($newmenu);

            var_dump($result);

    }

    public function getJsSign() {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');
//        header("Content-type: application/json");
        $url = $this->input->post('url');
//        var_dump($url);
        $return = $this->ci_wechat->getJsSign($url);
//        var_dump($return);
        echo json_encode($return);exit;
    }

    public function oauth() {
           header("Access-Control-Allow-Origin:*");
           header('Access-Control-Allow-Headers: *');
           header('Access-Control-Allow-Methods: *');

           $this->db2 = $this->load->database('account',TRUE);

           $code = $this->input->post('code');

           $result = $this->ci_wechat->getOauthAccessToken($code);
           log_message("error","获取access token");
           log_message("error",var_export($result,TRUE));

           if(!$result) {
               log_message("error",$this->ci_wechat->errCode);
               log_message("error",$this->ci_wechat->errMsg);
               echo json_encode(array('status'=>false,'msg'=>$this->ci_wechat->errMsg));exit;
           } else {
               $this->db2->where('InsurePass',$result['unionid']);
               $query = $this->db2->get('AccountsInfo');
               $role = $query->row_array();

               // 查询是否绑定代理
               $this->db->where('role_id',$role['UserID']);
               $query = $this->db->get('adminer');
               $adminer = $query->row_array();

               $role['is_agent'] = $adminer?1:0;

               if($role) {
                   $this->db3 = $this->load->database('treasure',TRUE);
                   $this->db3->where('UserID',$role['UserID']);
                   $query = $this->db3->get('GameScoreInfo');
                   $result = $query->row_array();

                   $role['InsureScore']  = $result['InsureScore'];

                   echo json_encode(array('status'=>true,'msg'=>'','data'=>$role));exit;
               } else {
                   echo json_encode(array('status'=>false,'code'=>1,'msg'=>'未查询到游戏角色'));exit;
               }

               // 获取user_info
             // 查找是否存在
//             $this->db->where('openid',$result['openid']);
//             $query = $this->db->get('mp_user');
//             $user = $query->row_array();
//             if(!$user) {
//                 $user = $this->ci_wechat->getOauthUserinfo($result['access_token'],$result['openid']);
//                 log_message("error","获取用户信息");
//                 log_message("error",var_export($user,TRUE));
//
//                 if(!$user) {
//                    log_message("error",$this->ci_wechat->errMsg);
//                    log_message("error",$this->ci_wechat->errCode);
//                     echo json_encode(array('status'=>false,'msg'=>$this->ci_wechat->errMsg));exit;
//                 } else {
////                       $user['create_time'] = date('Y-m-d H:i:s');
////                       unset($user['privilege']);
////                       $this->db->insert('mp_user',$user);
//                     // 查询对应的游戏角色
//
//                 }
//             }


           }


    }

     public function createOrder() {
            header("Access-Control-Allow-Origin:*");
            header('Access-Control-Allow-Headers: *');
            header('Access-Control-Allow-Methods: *');
    //        header("Content-type: application/json");
            $money = $this->input->post('money');
            $money = str_replace('元','',$money);
            $uid = $this->input->post('uid');
//            //①、
            $this->db->where('openid',$openid);
            $query = $this->db->get('mp_user');
            $user = $query->row_array();

            if(!$user) {
               echo json_encode(array('status'=>false,'msg'=>'用户不存在'));exit;
            }
            $this->load->helper('string');

            $order_no = date("YmdHis").random_string('nozero', 3);

            // 创建订单
            $data = array(
                'user_id' => $user['user_id'],
                'order_no'=>  $order_no,
                'total_fee'   =>  $money,
                'create_time' => date('Y-m-d H:i:s')
            );

            $this->db->insert('mp_order',$data);

            if($order_id = $this->db->insert_id()>0) {
                  //②、统一下单
                 $input = new WxPayUnifiedOrder();
                 $input->SetBody($money.'元');
                 $input->SetAttach($order_id);
                 $input->SetOut_trade_no($order_no);
                 $input->SetTotal_fee($money*100);
                 $input->SetTime_start(date("YmdHis"));
                 $input->SetTime_expire(date("YmdHis", time() + 600));
                 $input->SetGoods_tag("");
                 $input->SetNotify_url("http://lhmh.tuo3.com.cn/server/index.php/api/v1/weixin/notify");
                 $input->SetTrade_type("JSAPI");
                 $input->SetOpenid($openid);
                 $order = WxPayApi::unifiedOrder($input);

                 if($order['return_code']=='SUCCESS' && $order['result_code'] == 'SUCCESS') {
                      $tools = new JsApiPay();
                     $jsApiParameters = $tools->GetJsApiParameters($order);
                     log_message("error",var_export($jsApiParameters,TRUE));
                     echo json_encode(array('status'=>true,'msg'=>'','data'=>$jsApiParameters,'order'=>$order_no));exit;
                 } else {
                     echo json_encode(array('status'=>false,'msg'=>$order['return_msg']));exit;
                 }
            } else {
                  echo json_encode(array('status'=>false,'msg'=>'订单创建失败'));exit;
            }



    }



}
