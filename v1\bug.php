    <?php

/**
 * 语音接口
 */

class Bug extends CI_Controller
{

        public function __construct()
    {
        parent::__construct();
        $this->load->model('api_model','',TRUE);
    }

    //上传接口
    public function upload()
    {
        $error = $this->input->post('error');

        if(!empty($error)) {
            $data = array(
                'error'=>$error,
                'create_time'=>time()
                );

            $this->db->insert('bug',$data);

            if($this->db->insert_id()>0){
                echo json_encode(array('code'=>0,'msg'=>'上传成功'));exit;
            } else {
                echo json_encode(array('code'=>1,'msg'=>'上传失败'));exit;
            }
        } else {
            echo json_encode(array('code'=>1,'msg'=>'未接受到内容'));exit;
        }
    }

    public function show()
    {
        header("Content-type: text/html; charset=utf-8"); 

        $this->db->order_by('id','DESC');

        $query = $this->db->get('bug');

        $records = $query->result_array();

        echo "<h1>".count($records)."条记录</h1>";

        // echo '<table style="">';
        // echo "<tr><th>编号</th><th>上报时间</th><th>错误详情</th></tr>";

        foreach ($records as $key => $record) {
            // echo "<tr>";
            // echo "<td>".$record['id']."</td>";
            // echo "<td>".$record['create_time']."</td>";
            echo "<fieldset>";
            echo "<legend>编号:".$record['id'].',';
            echo date('Y-m-d H:i:s',$record['create_time']).'</legend>';
            echo '<div style="background-color:#000;padding:5px;color:#ccc;">'.nl2br($record['error']).'</div>';
            echo "</fieldset>";
            echo "<br>";
        }

        // echo "</table>";
    }

    //上传接口
    public function upload1()
    {
        $error = $this->input->post('error');

        if(!empty($error)) {
            $data = array(
                'error'=>$error,
                'create_time'=>time()
                );

            $this->db->insert('bug1',$data);

            if($this->db->insert_id()>0){
                echo json_encode(array('code'=>0,'msg'=>'上传成功'));exit;
            } else {
                echo json_encode(array('code'=>1,'msg'=>'上传失败'));exit;
            }
        } else {
            echo json_encode(array('code'=>1,'msg'=>'未接受到内容'));exit;
        }
    }

    public function show1()
    {
        header("Content-type: text/html; charset=utf-8"); 

        $this->db->order_by('id','DESC');

        $query = $this->db->get('bug1');

        $records = $query->result_array();

        echo "<h1>".count($records)."条记录</h1>";

        // echo '<table style="">';
        // echo "<tr><th>编号</th><th>上报时间</th><th>错误详情</th></tr>";

        foreach ($records as $key => $record) {
            // echo "<tr>";
            // echo "<td>".$record['id']."</td>";
            // echo "<td>".$record['create_time']."</td>";
            echo "<fieldset>";
            echo "<legend>编号:".$record['id'].',';
            echo date('Y-m-d H:i:s',$record['create_time']).'</legend>';
            echo '<div style="background-color:#000;padding:5px;color:#ccc;">'.nl2br($record['error']).'</div>';
            echo "</fieldset>";
            echo "<br>";
        }

        // echo "</table>";
    }

}