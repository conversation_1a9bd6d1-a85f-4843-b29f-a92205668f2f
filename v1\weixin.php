<?php

defined('BASEPATH') OR exit('No direct script access allowed');

// This can be removed if you use __autoload() in config.php OR use Modular Extensions
/** @noinspection PhpIncludeInspection */
require(APPPATH . 'libraries/REST_Controller.php');
require_once(APPPATH . 'libraries/wxpay/WxPay.JsApiPay.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Api.php');
require_once(APPPATH . 'libraries/wxpay/lib/WxPay.Config.php');

class Weixin extends CI_Controller
{

    function __construct()
    {
        parent::__construct();
        $this->load->model('mp_model');
        $this->load->model('player_model');
        $this->load->model('server_model');
    }

    public function index()
    {
        $game_id = $this->input->get('game_id');

        $this->load->library('CI_Wechat', array('game_id' => $game_id));
        // 如果首次配置验
//       log_message("error", var_export($_GET,true));
//        $return = $this->ci_wechat->valid(true);
//        echo $return;exit;
//        log_message("error", $return);


//      $text = "您好，欢迎关注小白娱乐麻将公众号！如有问题，请直接回复公众号，我们将有人工客服为你解答！\n\n<a href='https://lhmj.tuo3.com.cn/wap/#/tip'>1.点击：申请会员</a>\n\n<a href='https://lhmj.tuo3.com.cn/wap/#/pay'>2.点击：房卡购买</a>\n\n<a href='http://a.mlinks.cc/AaHx'>3.点击：下载游戏</a>\n\n<a href='https://lhmj.tuo3.com.cn/server2/'>4.点击：会员后台</a>";
        $type = $this->ci_wechat->getRev()->getRevType();

        switch ($type) {
            case Wechat::MSGTYPE_TEXT:
                $content = $this->ci_wechat->getRev()->getRevContent();

                //模糊搜索
                $res = $this->mp_model->fuzzy_search($game_id, 3, $content);

                //如果关键字不为空
                if (!empty($res)) {
                    //如果是文本
                    if ($res['reply_type'] == 'text') {
                        $this->ci_wechat->text($res['reply_content'])->reply();
                    } else if ($res['reply_type'] == 'news') {
                        //如果是图文
                        $this->ci_wechat->news(json_decode($res['reply_content'], TRUE))->reply();
                    }
                }else{
                    $customer = $this->ci_wechat->getCustomServiceOnlineKFlist();
                    log_message("error",'customer');
                    log_message("error", var_export($customer, TRUE));

                    if ($customer['kf_online_list']){
                        if (count($customer) <= 1 ){
                            foreach ($customer['kf_online_list'] as $key=>$value){
                                //$role_num[] = $value['accepted_case'];
                                $customer_account = $value['kf_account'];
                            }
                            if ($customer_account){
                                $this->ci_wechat->transfer_customer_service($customer_account)->reply();
                            }
                        }else{
                            $reply_content = '目前暂无客服在线';
                            $this->ci_wechat->text($reply_content)->reply();
                        }
                    }

/*                    log_message("error",'role_num111');
                    log_message("error", var_export($role_num, TRUE));

                    array_multisort($role_num,SORT_ASC,$customer);

                    log_message("error",'customer1111');
                    log_message("error", var_export($customer, TRUE));

                    $customer_account = $customer[0];

                    log_message("error",'customer_account');
                    log_message("error", var_export($customer_account, TRUE));*/
                }
                /*                if (strstr($content, '卡') || strstr($content, '充值') || strstr($content, '买') ) {
                                    $reply = $this->mp_model->get_mp_reply($game_id,3,'卡');

                                    if($reply['reply_type'] == 'text') {
                                        $this->ci_wechat->text($reply['reply_content'])->reply();
                                    }
                                }


                                if (strstr($content, '下载') || strstr($content, '游戏') || strstr($content, '临海') || strstr($content, '麻将')) {
                                    $reply = $this->mp_model->get_mp_reply($game_id,3,'游戏');

                                    if ($reply['reply_type'] == 'text') {
                                        $this->ci_wechat->text($reply['reply_content'])->reply();
                                    }
                                }

                                if (strstr($content, '代理') || strstr($content, '合伙人') || strstr($content, '招聘') || strstr($content , '会员') || strstr($content, '申请') || strstr($content, '推广')) {
                                    $reply = $this->mp_model->get_mp_reply($game_id,3,'代理');

                                    if ($reply['reply_type'] == 'text') {
                                        $this->ci_wechat->text($reply['reply_content'])->reply();
                                    }
                                }

                                if (strstr($content, '卡') || strstr($content, '玩不了') || strstr($content, '问题')) {
                                    $reply = $this->mp_model->get_mp_reply($game_id,3,'FAQ');

                                    if ($reply['reply_type'] == 'text') {
                                        $this->ci_wechat->text($reply['reply_content'])->reply();
                                    }
                                }*/
                break;
            case Wechat::MSGTYPE_EVENT:
                $event = $this->ci_wechat->getRev()->getRevEvent();
                switch ($event['event']) {
                    //订阅自动回复
                    case Wechat::EVENT_SUBSCRIBE:
                        $reply = $this->mp_model->get_mp_reply($game_id, 1);
                        if ($reply['reply_type'] == 'text') {
                            $this->ci_wechat->text($reply['reply_content'])->reply();
                        } else if ($reply['reply_type'] == 'news') {
                            $this->ci_wechat->news(json_decode($reply['reply_content'], TRUE))->reply();
                        }
                        break;
                    case Wechat::EVENT_MENU_CLICK:

                        $menu = $this->mp_model->get_mp_menu($game_id, $event['key']);

                        if ($menu && $menu['type'] == 'click') {
                            if ($menu['reply_type'] == 'text') {
                                $this->ci_wechat->text($menu['reply_content'])->reply();
                            } else if ($menu['reply_type'] == 'news') {
                                $this->ci_wechat->news(json_decode($menu['reply_content'], TRUE))->reply();
                            }
                        }
                        break;
                    default:
                        break;
                }
                break;
            case Wechat::MSGTYPE_IMAGE:
                break;
            default:
                break;
        }
    }

    //创建菜单
    public function create_menu()
    {
        $game_id = $this->input->get('game_id');
        $this->load->library('CI_Wechat', array('game_id' => $game_id));
        //获取全部父菜单
        $father_menu = $this->mp_model->get_parent_menu($game_id);
        //循环
        $new_menu = [];
        foreach ($father_menu as $key => $value) {
            $new_menu['button'][$key]['name'] = $value['name'];
            //获取子菜单
            $child_menu = $this->mp_model->get_parent_menu($game_id, $value['id']);
            //循环分类
            foreach ($child_menu as $k => $v) {
                if ($v['type'] == 'click') {
                    $new_menu['button'][$key]['sub_button'][$k]['name'] = $v['name'];
                    $new_menu['button'][$key]['sub_button'][$k]['type'] = $v['type'];
                    $new_menu['button'][$key]['sub_button'][$k]['key'] = $v['key'];
                }
                if ($v['type'] == 'view') {
                    $new_menu['button'][$key]['sub_button'][$k]['name'] = $v['name'];
                    $new_menu['button'][$key]['sub_button'][$k]['type'] = $v['type'];
                    $new_menu['button'][$key]['sub_button'][$k]['key'] = $v['key'];
                    $new_menu['button'][$key]['sub_button'][$k]['url'] = $v['url'];
                }
            }
        }
        //创建菜单
        $result = $this->ci_wechat->createMenu($new_menu);

        //如果创建成功
        if ($result) {
            $res['code'] = 1;//创建成功
        } else {
            $res['code'] = 0;//创建失败
        }

        echo json_encode($res);
//        $newmenu =  array(
//            "button"=>
//                array(
//                    array(
//                        'name'=>'客服中心',
//                        'sub_button'=>array(
//                            array(
//                                "type"=>"click",
//                                "name"=>"人工客服",
//                                "key"=>"menu_customer"
//                            ),
//                            array(
//                                "type"=>"click",
//                                "name"=>"官方声明",
//                                "key"=>"menu_notice"
//                            ),
//                            array(
//                                "type"=>"click",
//                                "name"=>"最新活动",
//                                "key"=>"menu_activity",
//                            ),
//                        )
//                    ),
//                    array(
//                        'name'=>'游戏下载',
//                        'sub_button'=>array(
//                            array(
//                                "type"=>"view",
//                                "name"=> "游戏下载地址",
//                                "key"=>"menu_download",
//                                "url"=>"http://a.mlinks.cc/AKKX",
//                            ),
//                            array(
//                                "type"=>"click",
//                                "name"=> "游戏下载说明",
//                                "key"=>"menu_download_desc",
//                            ),
//                            array(
//                                "type"=>"click",
//                                "name"=>"苹果安装说明",
//                                "key"=>"menu_ios_install",
//                            )
//                        )
//                    ),
//                    array(
//                        'name'=>'游戏充值',
//                        'sub_button'=>array(
////                                    array(
////                                        "type"=>"click",
////                                        "name"=> "申请代理",
////                                        "key"=>"menu_agent_apply",
////                                    ),
//                            array(
//                                "type"=>"view",
//                                "name"=>"会员充值",
//                                "key"=>"menu_card_buy",
//                                "url"=>"https://lhmj.tuo3.com.cn/wap/#/pay?game_id=7",
//                            ),
//                            array(
//                                "type"=>"view",
//                                "name"=>"会员登录",
//                                "key"=>"menu_agent_login",
//                                "url"=>"https://lhmj.tuo3.com.cn/server2/",
//                            )
////                                     array(
////                                        "type"=>"view",
////                                        "name"=>"代理后台",
////                                        "key"=>"menu_agent_admin",
////                                        "url"=>"https://lhmj.tuo3.com.cn/wap/#/tip",
////                                    ),
////                                    array(
////                                        "type"=>"view",
////                                        "name"=>"代理套餐",
////                                        "key"=>"menu_agent_buy",
////                                        "url"=>"https://lhmj.tuo3.com.cn/wap/#/tip",
////                                    )
//                        )
//                    ),
//                )
//        );

        /*        $newmenu = array(
                    "button" =>
                        array(
                            array(
                                'name' => '客服中心',
                                'sub_button' => array(
                                    array(
                                        "type" => "click",
                                        "name" => "人工客服",
                                        "key" => "menu_customer"
                                    ),
                                    array(
                                        "type" => "click",
                                        "name" => "官方声明",
                                        "key" => "menu_notice"
                                    ),
                                    array(
                                        "type" => "click",
                                        "name" => "常见问题",
                                        "key" => "menu_problem"
                                    )
                                )
                            ),
                            array(
                                'name' => '游戏下载',
                                'sub_button' => array(
                                    array(
                                        "type" => "view",
                                        "name" => "游戏下载地址",
                                        "key" => "menu_download",
                                        "url" => "http://a.mlinks.cc/AaHx"
                                    ),
                                    array(
                                        "type" => "click",
                                        "name" => "游戏下载说明",
                                        "key" => "menu_download_desc"
                                    )
        //                            array(
        //                                "type" => "click",
        //                                "name" => "苹果安装说明",
        //                                "key" => "menu_ios_install",
        //                            )
                                )
                            ),
                            array(
                                'name' => '游戏充值',
                                'sub_button' => array(
        //                                    array(
        //                                        "type"=>"click",
        //                                        "name"=> "申请代理",
        //                                        "key"=>"menu_agent_apply",
        //                                    ),
                                    array(
                                        "type" => "click",
                                        "name" => "申请推广员",
                                        "key" => "menu_apply_generalize"
                                    ),
                                    array(
                                        "type" => "view",
                                        "name" => "充值",
                                        "key" => "menu_roomcard_buy",
                                        "url" => "https://lhmj.tuo3.com.cn/wap/#/pay?game_id=6"
                                    ),
                                    array(
                                        "type" => "view",
                                        "name" => "会员登录",
                                        "key" => "menu_agent_login",
                                        "url" => "https://lhmj.tuo3.com.cn/server2/"
                                    )
        //                                     array(
        //                                        "type"=>"view",
        //                                        "name"=>"代理后台",
        //                                        "key"=>"menu_agent_admin",
        //                                        "url"=>"https://lhmj.tuo3.com.cn/wap/#/tip",
        //                                    ),
        //                                    array(
        //                                        "type"=>"view",
        //                                        "name"=>"代理套餐",
        //                                        "key"=>"menu_agent_buy",
        //                                        "url"=>"https://lhmj.tuo3.com.cn/wap/#/tip",
        //                                    )
                                )
                            ),
                        )
                );*/


    }

    public function getJsSign()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');
//        header("Content-type: application/json");
        $url = $this->input->post('url');
        $game_id = $this->input->post('game_id');

        $this->load->library('CI_Wechat', array('game_id' => $game_id));

//        var_dump($url);
        $return = $this->ci_wechat->getJsSign($url);
//        var_dump($return);
        echo json_encode($return);
        exit;
    }

    public function oauth()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $code = $this->input->post('code');
        $game_id = $this->input->post('game_id');
        $this->load->library('CI_Wechat', array('game_id' => $game_id));

        $mp_config = $this->mp_model->get_mp_config($game_id);

        $this->load->model('user_model');
        $this->load->model('player_model');
        $this->player_model->set_database($game_id);

        // 获取TOKEN
        $token = $this->ci_wechat->getOauthAccessToken($code);

        log_message("error", "获取access token");
        log_message("error", var_export($token, TRUE));

        if (!$token) {
            log_message("error", $this->ci_wechat->errCode);
            log_message("error", $this->ci_wechat->errMsg);
            echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg, 'is_bind' => false, 'is_agent' => false));
            exit;
        }

        // 首先查询本地
        $user = $this->mp_model->get_mp_user_by_openid($token['openid']);


        // 未注册
        if (!$user) {
            $user = $this->ci_wechat->getOauthUserinfo($token['access_token'], $token['openid']);
            log_message("error", "获取用户信息");
            log_message("error", var_export($user, TRUE));

            if (!$user) {
                log_message("error", $this->ci_wechat->errMsg);
                log_message("error", $this->ci_wechat->errCode);
                echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg, 'is_bind' => false, 'is_agent' => false));
                exit;
            } else {
                $user['reg_time'] = time();
                $user['role_id'] = 0;
                $user['game_id'] = $game_id;

                unset($user['privilege']);
                $this->db->insert('mp_user', $user);
            }
        }

        if ($user['role_id'] > 0) {
            // 已绑定
            $role = $this->player_model->get_role_info($user['role_id']);
        } else {
            // 未绑定
            $role = $this->player_model->get_role_info_by_useruin($user['unionid']);

            if ($role) {
                $this->db->where('openid', $token['openid']);
                $this->db->update('mp_user', array('role_id' => $role['UserID']));
            }
        }

        if (!$role) {
            echo json_encode(array('status' => false, 'msg' => '未绑定游戏角色，请下载并登录', 'is_bind' => false, 'is_agent' => false));
            exit;
        }

/*        if (in_array($game_id,array(16,17))){
            echo json_encode(array('status' => false, 'msg' => '绑定成功,请关闭窗口重新提现;', 'is_bind' => false, 'is_agent' => false));
            exit();
        }*/

        // 查询角色房卡
        $score = $this->player_model->get_role_score($role['UserID']);

        // 查询是否绑定代理
        $adminer = $this->user_model->get_one_agent($role['UserID'], $game_id);

        //游戏
/*        $game = array(9, 15);
        //判断当前游戏id是否存在游戏中
        if (in_array($game_id, $game)) {
            //获取全部代理
            $adminer = $this->user_model->get_all_agency($role['UserID'], $game_id);
            //如果是代理 3普通代理 8总代
            if ($adminer) {
                //如果是总代
                if ($adminer['group'] == 8) {
                    // 获取商品信息
                    $goods = $this->mp_model->get_mp_good_by_type(3, $game_id);

                    if (empty($goods)) {
                        echo json_encode(array('status' => false, 'msg' => '充值功能暂未开放,敬请期待;', 'is_bind' => false, 'is_agent' => false));
                        exit();
                    }

                    echo json_encode(array('status' => true, 'msg' => '', 'is_bind' => true, 'is_agent' => true, 'user' => array('NickName' => $role['NickName'], 'UserID' => $role['UserID'], 'InsureScore' => $adminer['score'], 'account' => $adminer['username']), 'goods' => $goods));
                    exit;
                    //如果是普通代理
                } elseif ($adminer['group'] == 3) {
                    //如果绑定了总代理
                    if (!empty($adminer['spreader_id'])) {
                        echo json_encode(array('status' => false, 'msg' => '代理充值请联系总代;', 'is_bind' => false, 'is_agent' => false));
                        exit();
                        //如果没有绑定总代理
                    } else {
                        // 获取商品信息
                        $goods = $this->mp_model->get_mp_good_by_type(2, $game_id);

                        if (empty($goods)) {
                            echo json_encode(array('status' => false, 'msg' => '充值功能暂未开放,敬请期待;', 'is_bind' => false, 'is_agent' => false));
                            exit();
                        }

                        echo json_encode(array('status' => true, 'msg' => '', 'is_bind' => true, 'is_agent' => true, 'user' => array('NickName' => $role['NickName'], 'UserID' => $role['UserID'], 'InsureScore' => $adminer['score'], 'account' => $adminer['username']), 'goods' => $goods));
                        exit;
                    }
                } else {
                    echo json_encode(array('status' => false, 'msg' => '充值失败,请联系管理员;', 'is_bind' => false, 'is_agent' => false));
                    exit();
                }
            } else {
                //如果是玩家
                $goods = $this->mp_model->get_mp_good_by_type(1, $game_id);

                if (empty($goods)) {
                    echo json_encode(array('status' => false, 'msg' => '充值功能暂未开放,敬请期待;', 'is_bind' => false, 'is_agent' => false));
                    exit();
                }

                echo json_encode(array('status' => true, 'msg' => '', 'is_bind' => true, 'is_agent' => false, 'user' => array('NickName' => $role['NickName'], 'UserID' => $role['UserID'], 'InsureScore' => $score['InsureScore']), 'goods' => $goods, 'gm' => $mp_config['gm']));
                exit();
            }
            exit();
        }*/

        $result = $this->mp_model->get_role_is_first_order($role['UserID'],$game_id);//获取用户首充

	    $goods_id = array();
        if (!empty($result)){//用户已经充值
            foreach ($result as $key=>$res){//获取用户充值的商品ID
                $goods_id[$key] = $res['good_id'];
            }
        }

        //如果已绑定代理
        if ($adminer) {

            // 判断有效绑定的玩家个数
            /*            $this->db->where('agent_id', $adminer['id']);
                        $this->db->where('is_play', 1);
                        $query = $this->db->get('role_bind');
                        $roles = $query->result_array();*/

/*            if (in_array($game_id, array(6, 7))/* || count($roles) >= 5) {
                // 判断是否首充
//                $this->db->join('mp_good', 'mp_good.id=mp_order.good_id');
                $this->db->where('role_id', $role['UserID']);
//                $this->db->where('mp_good.game_id', $game_id);
                $this->db->where('mp_order.game_id', $game_id);
                $this->db->where('mp_order.is_first', 1);
                $this->db->where('mp_order.status', 1);
                $query = $this->db->get('mp_order');
                $result = $query->result_array();

                // 获取计费ID
                $this->db->where('type', 2);
                if ($result) { // 剔除首充商品
                    $this->db->where('is_first', 0);
                }
                $this->db->where('game_id', $game_id);
                $query = $this->db->get('mp_good');
                $goods = $query->result_array();

                echo json_encode(array('status' => true, 'msg' => '', 'is_bind' => true, 'is_agent' => true, 'user' => array('NickName' => $role['NickName'], 'UserID' => $role['UserID'], 'InsureScore' => $adminer['score'], 'account' => $adminer['username']), 'goods' => $goods));
                exit;
            }*/
/*                if(in_array($game_id, array(30))){
                    // 获取计费ID
                    $goods = $this->mp_model->get_mp_good_by_type(2, $game_id);

                    echo json_encode(array('status' => true, 'msg' => '', 'is_bind' => true, 'is_agent' => false, 'user' => array('NickName' => $role['NickName'], 'UserID' => $role['UserID'], 'InsureScore' => $score['RoomCard'],'account' => $adminer['username']), 'goods' => $goods, 'gm' => $mp_config['gm']));
                    exit;
                } else {
                // 获取计费ID
                $goods = $this->mp_model->get_mp_good_by_type(2, $game_id);

                echo json_encode(array('status' => true, 'msg' => '', 'is_bind' => true, 'is_agent' => true, 'user' => array('NickName' => $role['NickName'], 'UserID' => $role['UserID'], 'InsureScore' => $adminer['score'], 'account' => $adminer['username']), 'goods' => $goods));
                exit;
            }*/
            $goods = $this->mp_model->get_mp_good_by_type(2, $game_id,false,$goods_id);//获取商品

            echo json_encode(array('status' => true, 'msg' => '', 'is_bind' => true, 'is_agent' => true, 'user' => array('NickName' => $role['NickName'], 'UserID' => $role['UserID'], 'InsureScore' => $adminer['score'], 'account' => $adminer['username']), 'goods' => $goods));
        } else {
/*            if($game_id == 37){
                echo json_encode(array('status' => true, 'msg' => '','is_player'=>true));
                exit;
            }*/
            $goods = $this->mp_model->get_mp_good_by_type(1, $game_id,false,$goods_id);//获取商品

            echo json_encode(array('status' => true, 'msg' => '', 'is_bind' => true, 'is_agent' => false, 'user' => array('NickName' => $role['NickName'], 'UserID' => $role['UserID'], 'InsureScore' => $score['RoomCard']), 'goods' => $goods, 'gm' => $mp_config['gm']));
            exit;
        }
    }


    public function oauth2()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        log_message("error","微信登录接口");
        log_message("error",var_export($_POST,true));


        $code = $this->input->post('code');
        $channel_id = $this->input->post('channel_id');

//        echo json_encode(array('status' => true, 'msg' => '','data'=>array('pv_count'=>1111)));exit;

        $channel = $this->server_model->get_one_channel($channel_id);

        if(!$channel) {
            echo json_encode(array('status' => false, 'msg' => '渠道不存在'));
        }

        $game = $this->server_model->get_game_by_id($channel['game_id']);

        $this->load->library('CI_Wechat', array('game_id' => $channel['game_id']));

        $mp_config = $this->mp_model->get_mp_config($channel['game_id']);

        $this->load->model('user_model');
        $this->load->model('player_model');
        $this->player_model->set_database($channel['game_id']);

        // 获取TOKEN
        $token = $this->ci_wechat->getOauthAccessToken($code);
        log_message("error", "获取access token");
        log_message("error", var_export($token, TRUE));

        if (!$token) {
            log_message("error", $this->ci_wechat->errCode);
            log_message("error", $this->ci_wechat->errMsg);
            echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg));
            exit;
        }

        // 首先查询本地
        $user = $this->mp_model->get_mp_user_by_openid2($token['openid']);

        // 未注册
        if (!$user) {
            $user = $this->ci_wechat->getOauthUserinfo($token['access_token'], $token['openid']);
            log_message("error", "获取用户信息");
            log_message("error", var_export($user, TRUE));

            if (!$user) {
                log_message("error", $this->ci_wechat->errMsg);
                log_message("error", $this->ci_wechat->errCode);
                echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg));
                exit;
            } else {
                $user['reg_time'] = time();
                $user['role_id'] = 0;
                $user['game_id'] = $channel['game_id'];

                unset($user['privilege']);
                $this->db->insert('mp_user2', $user);

                $user_id = $this->db->insert_id();
                $this->db->where('user_id',$user_id);
                $query = $this->db->get('mp_user2');
                $user = $query->row_array();
            }
        }

        if ($user['role_id'] > 0) {
            // 已绑定
            $role = $this->player_model->get_role_info($user['role_id']);
        } else {
            // 未绑定
            $role = $this->player_model->get_role_info_by_useruin($user['unionid']);

            if ($role) {
                $this->db->where('openid', $token['openid']);
                $this->db->update('mp_user2', array('role_id' => $role['UserID']));
            }
        }

        $data = [];

        $data['game_name'] = $game['game_name'];

        $data['down_url'] = $game['magicwindow_url'];

        $is_new = $role?false:true;
        $data['is_new'] = $is_new;
        $this->db->where('game_id',$game['game_id']);
        $query = $this->db->get('mp_user2');
        $data['pv_count'] = ($query->num_rows()+7)*11;

        $data['avatar_url'] = $user['headimgurl'];

        $is_lost= false;

        if(!$is_new) {

            $role_info2 = $this->player_model->get_role_score2($role['UserID']);

            $data['invite_url'] = "https://lhmj.tuo3.com.cn/h5/#/invite?channel_id=".$channel_id.'&role_id='.$role['UserID'];

            $reg_date = substr($role['RegisterDate'],0,10);
            $reg_days = ceil((time()-strtotime($reg_date))/(3600*24));

            $data['reg_date'] = $reg_date;
            $data['reg_days'] = $reg_days;

            $login_count = $role['GameLogonTimes'];

            $data['login_count'] = $login_count;

            $play_total_count = $role_info2['WinCount']+$role_info2['LostCount']+$role_info2['DrawCount'];

            $data['play_total_count'] = $play_total_count;

            $play_win_count = $role_info2['WinCount'];

            $data['play_win_count'] = $play_win_count;

            $play_win_rate = $play_total_count>0?round($play_win_count*100/$play_total_count):0;

            $over_win_rate = ($play_win_rate*2+1)>99?99:($play_win_rate*2+1);

            $data['play_win_rate'] = $play_win_rate;
            $data['over_win_rate'] = $over_win_rate;

            $last_login_date = substr($role['LastLogonDate'],0,19);

            $diff_days = ceil((time()-strtotime($last_login_date))/86400);

            $is_lost = $diff_days>7?true:false;

            $data['is_lost'] = $is_lost;

            $last_login_text = $diff_days.'天前';

            $data['last_login_days'] = $diff_days;

            if($diff_days >3) {
                switch ($diff_days) {
                    case 0:
                        $last_login_text = '今天';
                        break;
                    case 1:
                        $last_login_text = '昨天';
                        break;
                    case 2:
                        $last_login_text = '前天';
                        break;
                    default:
                        break;
                }
            }

            $data['last_login_text'] = $last_login_text;

            if($is_lost) {
                $data['friend_count'] = rand(20,50);
                $data['last_login_text'] = substr($last_login_date,0,10);

            } else {
                $data['friend_count'] = rand(5,20);
            }

        }

        if($is_lost) {

            $from_role_id = $this->input->post('role_id');

            if($from_role_id && $from_role_id != 'null') {
                $from_role = $this->player_model->get_role_info($from_role_id);

                if($from_role) {
                    // 判断是否有相同记录
                    $this->db->where('to_role_id',$user['role_id']);
                    $query = $this->    db->get('callback_logs');

                    if($query->num_rows() == 0) {
                        // 插入记录
                        $db_data = array(
                            'from_role_id'=>$from_role_id,
                            'to_role_id'=>$user['role_id'],
                            'to_user_id'=>$user['user_id'],
                            'game_id'=>$game['game_id'],
                            'user_type'=>2,
                            'create_time'=>time()
                        );

                        $this->db->insert('callback_logs',$db_data);
                    }
                }
            }
        }




        echo json_encode(array('status' => true, 'msg' => '','data'=>$data));exit;
    }

    //获取游戏id
    public function get_game_id()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $this->load->model('server_model');

        $channel_id = $this->input->post('channel_id');

        $channel = $this->server_model->get_game_by_channel_id($channel_id);

        $game = $this->server_model->get_game_by_id($channel['game_id']);

        if($game){
            echo json_encode(array('code'=>1,'game_id'=>$game['game_id'],'game_name'=>$game['game_name'],'game_icon'=>$game['landing_icon_url'],'down_url'=>$game['magicwindow_url']));exit();
        }else{
            echo json_encode(array('code'=>0));exit();
        }
    }

    //绑定公众号(mp_)
    public function bind_wechat()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $code = $this->input->post('code');
        $game_id = $this->input->post('game_id');
        $this->load->library('CI_Wechat', array('game_id' => $game_id));

        $this->load->model('user_model');
        $this->load->model('player_model');
        $this->player_model->set_database($game_id);

        // 获取TOKEN
        $token = $this->ci_wechat->getOauthAccessToken($code);
        log_message("error", "获取access token");
        log_message("error", var_export($token, TRUE));

        if (!$token) {
            log_message("error", $this->ci_wechat->errCode);
            log_message("error", $this->ci_wechat->errMsg);
            echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg, 'is_bind' => false, 'is_agent' => false));
            exit;
        }

        // 首先查询本地
        $user = $this->mp_model->get_mp_user_by_openid($token['openid']);

        // 未注册
        if (!$user) {
            $user = $this->ci_wechat->getOauthUserinfo($token['access_token'], $token['openid']);
            log_message("error", "获取用户信息");
            log_message("error", var_export($user, TRUE));

            if (!$user) {
                log_message("error", $this->ci_wechat->errMsg);
                log_message("error", $this->ci_wechat->errCode);
                echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg, 'is_bind' => false, 'is_agent' => false));
                exit;
            } else {
                $user['reg_time'] = time();
                $user['role_id'] = 0;
                $user['game_id'] = $game_id;

                unset($user['privilege']);
                $this->db->insert('mp_user', $user);
            }
        }

        if ($user['role_id'] > 0) {
            // 已绑定
            $role = $this->player_model->get_role_info($user['role_id']);
        } else {
            // 未绑定
            $role = $this->player_model->get_role_info_by_useruin($user['unionid']);

            if ($role) {
                $this->db->where('openid', $token['openid']);
                $this->db->update('mp_user', array('role_id' => $role['UserID']));
            }
        }

        if ($role) {
            echo json_encode(array('status' => true, 'msg' => '绑定成功,请重新领取;', 'is_bind' => true, 'is_agent' => false));
            exit();
        }else{
            echo json_encode(array('status' => false, 'msg' => '未绑定角色，请下载并登录', 'is_bind' => false, 'is_agent' => false));
            exit;
        }
    }

/*    public function public_platform()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $code = $this->input->post('code');
        $game_id = $this->input->post('game_id');

        $this->load->library('CI_Wechat', array('game_id' => $game_id));

        $mp_config = $this->mp_model->get_mp_config($game_id);

        $this->load->model('user_model');
        $this->load->model('player_model');
        $this->player_model->set_database($game_id);

        // 获取TOKEN
        $token = $this->ci_wechat->getOauthAccessToken($code);
        log_message("error", "获取access token");
        log_message("error", var_export($token, TRUE));

        if (!$token) {
            log_message("error", $this->ci_wechat->errCode);
            log_message("error", $this->ci_wechat->errMsg);
            echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg, 'is_bind' => false, 'is_agent' => false));
            exit;
        }

        // 首先查询本地
        $user = $this->mp_model->get_mp_user_by_openid($token['openid']);

        // 未注册
        if (!$user) {
            $user = $this->ci_wechat->getOauthUserinfo($token['access_token'], $token['openid']);
            log_message("error", "获取用户信息");
            log_message("error", var_export($user, TRUE));

            if (!$user) {
                log_message("error", $this->ci_wechat->errMsg);
                log_message("error", $this->ci_wechat->errCode);
                echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg, 'is_bind' => false, 'is_agent' => false));
                exit;
            } else {
                $user['reg_time'] = time();
                $user['role_id'] = 0;
                $user['game_id'] = $game_id;

                unset($user['privilege']);
                $this->db->insert('mp_user', $user);
            }
        }

        if ($user['role_id'] > 0) {
            // 已绑定
            $role = $this->player_model->get_role_info($user['role_id']);
        } else {
            // 未绑定
            $role = $this->player_model->get_role_info_by_useruin($user['unionid']);

            if ($role) {
                $this->db->where('openid', $token['openid']);
                $this->db->update('mp_user', array('role_id' => $role['UserID']));
            }
        }

        if (!$role) {
            echo json_encode(array('status' => false, 'msg' => '未绑定游戏角色，请下载并登录游戏', 'is_bind' => false, 'is_agent' => false));
            exit;
        }

        // 查询角色
        $score = $this->player_model->get_role_score($role['UserID']);

        //获取活动设置
        $activity = $this->mp_model->get_mp_all_activity_setting($game_id);

        //处理
        $data = $this->activity_dispose($activity,$role['UserID'],$game_id);
        //得分记录
        if (!empty($data['scoring_record'])){
            $scoring_record = $data['scoring_record'];
        }else{
            $scoring_record = '';
        }
        //局数排名
        if (!empty($data['bureau_ranking'])){
            $bureau_ranking = $data['bureau_ranking'];
        }else{
            $bureau_ranking = '';
        }
        //胜局排行
        if (!empty($data['victory_ranking'])){
            $victory_ranking = $data['victory_ranking'];
        }else{
            $victory_ranking = '';
        }
        //活动设置
        $activity_setting = $data['activity_setting'];

        //兑换记录
        $exchange_record = $this->player_model->get_user_exchange_record($role['UserID']);
        //兑换的物品
        $exchange_goods = $this->db->get_where('mp_exchange_goods', array('game_id' => $game_id))->result_array();

        echo json_encode(array('status' => true, 'msg' => '', 'is_bind' => true, 'is_wechat' => true, 'user' => array('HeadImager' => $user['headimgurl'], 'NickName' => $user['nickname'], 'RoleId' => $user['role_id'], 'InsureScore' => $score['InsureScore'],'DDScore'=>$score['DDScore']), 'exchange_record' => $exchange_record, 'exchange_goods' => $exchange_goods,'scoring_record'=>$scoring_record,'activity'=>$activity,'bureau_ranking'=>$bureau_ranking,'activity_setting'=>$activity_setting,'victory_ranking'=>$victory_ranking));
    }


    //判断
    public function is_exist($activity)
    {
        if (time() >= $activity['begin_time'] && time() <= $activity['end_time']){
            return true;
        }
        return false;
    }

    //更新信息
    public function updateInfo()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $user_id = $this->input->post('user_id');
        $game_id = $this->input->post('game_id');

        // 查询角色分
        $this->player_model->set_database($game_id);
        $score = $this->player_model->get_role_score($user_id);
        //兑换记录
        $exchange_record = $this->player_model->get_user_exchange_record($user_id);
        //兑换的物品
        $exchange_goods = $this->db->get_where('mp_exchange_goods', array('game_id' => $game_id))->result_array();
        $user = $this->mp_model->get_mp_user_by_roleid($user_id,$game_id);

        //获取活动设置
        $activity = $this->mp_model->get_mp_all_activity_setting($game_id);

        $data = $this->activity_dispose($activity,$user_id,$game_id);
        //得分记录
        if (!empty($data['scoring_record'])){
            $scoring_record = $data['scoring_record'];
        }else{
            $scoring_record = '';
        }
        //局数排名
        if (!empty($data['bureau_ranking'])){
            $bureau_ranking = $data['bureau_ranking'];
        }else{
            $bureau_ranking = '';
        }
        //胜局排行
        if (!empty($data['victory_ranking'])){
            $victory_ranking = $data['victory_ranking'];
        }else{
            $victory_ranking = '';
        }
        //活动设置
        $activity_setting = $data['activity_setting'];

        echo json_encode(array('user' => array('HeadImager' => $user['headimgurl'], 'NickName' => $user['nickname'], 'RoleId' => $user['role_id'], 'InsureScore' => $score['InsureScore'],'DDScore'=>$score['DDScore']), 'exchange_record' => $exchange_record, 'exchange_goods' => $exchange_goods,'scoring_record'=>$scoring_record,'activity'=>$activity,'bureau_ranking'=>$bureau_ranking,'activity_setting'=>$activity_setting,'victory_ranking'=>$victory_ranking));
    }

    //活动处理
    public function activity_dispose($activity,$user_id,$game_id)
    {
        $scoring_record = '';
        $bureau_ranking = '';
        $victory_ranking = '';
        $activity_setting = '';

        foreach ($activity as $value){
            $prize = json_decode($value['prize'],true);
            if($value['activity_id'] == 2 && $this->is_exist($value)){
                //获取得分记录
                $scoring_record = $this->player_model->get_user_scoring_record($user_id,date('Y-m-d',$value['begin_time']),date('Y-m-d',$value['end_time']));

                $activity_setting['scoring_rule'] = $value['activity_rule'];
                $activity_setting['switch_scoring_record'] = true;
                $activity_setting['scoring_title'] = $value['activities_title'];
            }
            if($value['activity_id'] == 3 && $this->is_exist($value)){
                if($value && $value['send_type'] == 1){
                    $bureau_ranking = $this->player_model->get_one_user_everyday_bureau_ranking($user_id);
                    if (!empty($bureau_ranking)) {
                        foreach ($bureau_ranking as $k=>$v){
                            for ($i=0;$i<count($prize);$i++){
                                if($v['rank'] >= $prize[$i]['min_rank'] && $v['rank'] <= $prize[$i]['max_rank']){
                                    if ($prize[$i]['prize_type'] == 1){
                                        $name = '房卡';
                                    }
                                    if ($prize[$i]['prize_type'] == 2){
                                        $name = '现金';
                                    }
                                    if ($prize[$i]['prize_type'] == 3){
                                        $name = '其他';
                                    }
                                    $bureau_ranking['prize'] = $name;
                                    break;
                                }else{
                                    $bureau_ranking['prize'] = '暂无奖励';
                                }
                            }
                        }
                        $data = $this->player_model->get_all_user_everyday_bureau_ranking();
                        foreach ($data as $key => $v) {
                            if ($v['rank'] == ($bureau_ranking['rank'] - 1)) {
                                $res = $v;
                            }
                        }
                        $bureau_ranking['differ'] = $res['num'] - $bureau_ranking['num'];
                    }else{
                        $bureau_ranking['prize'] = '暂无奖励';
                        $bureau_ranking['num'] = 0;
                        $bureau_ranking['rank'] = 0;
                        $bureau_ranking['differ'] = 0;
                    }
                    $bureau_ranking['time'] = date('Y-m-d',time());
                    $activity_setting['ranking_rule'] = $value['activity_rule'];
                    $activity_setting['switch_bureau_ranking'] = true;
                    $activity_setting['ranking_title'] = $value['activities_title'];
                }else if($value && $value['send_type'] == 2){
                    $bureau_ranking = $this->player_model->get_spell_one_user_bureau_ranking($user_id,date('Y-m-d', $value['begin_time']), date('Y-m-d', $value['end_time']));
                    if (!empty($bureau_ranking)) {
                        for ($i=0;$i<count($prize);$i++){
                            if($bureau_ranking['rank'] >= $prize[$i]['min_rank'] && $bureau_ranking['rank'] <= $prize[$i]['max_rank']){
                                if ($prize[$i]['prize_type'] == 1){
                                    $name = '房卡';
                                }
                                if ($prize[$i]['prize_type'] == 2){
                                    $name = '现金';
                                }
                                if ($prize[$i]['prize_type'] == 3){
                                    $name = '其他';
                                }
                                $bureau_ranking['prize'] = $name;
                                break;
                            }else{
                                $bureau_ranking['prize'] = '暂无奖励';
                            }
                        }
                        $data = $this->player_model->get_spell_all_user_bureau_ranking(date('Y-m-d', $value['begin_time']), date('Y-m-d', $value['end_time']));
                        if ($bureau_ranking['rank'] == 1){
                            $bureau_ranking['differ'] = 0;
                        }else{
                            foreach ($data as $key => $v) {
                                if ($v['rank'] == ($bureau_ranking['rank'] - 1)) {
                                    $res = $v;
                                }
                            }
                            $bureau_ranking['differ'] = $res['num'] - $bureau_ranking['num'];
                        }
                    }else{
                        $bureau_ranking['prize'] = '暂无奖励';
                        $bureau_ranking['num'] = 0;
                        $bureau_ranking['rank'] = 0;
                        $bureau_ranking['differ'] = 0;
                    }
                    $bureau_ranking['time'] = date('Y-m-d',$value['begin_time']).'    '.'至'.date('m-d',time());
                    $activity_setting['ranking_rule'] = $value['activity_rule'];
                    $activity_setting['switch_bureau_ranking'] = true;
                    $activity_setting['ranking_title'] = $value['activities_title'];
                }
            }
            if($value['activity_id'] == 4 && $this->is_exist($value)){
                                if($value && $value['send_type'] == 1){
                                    $victory_ranking = $this->player_model->get_one_user_everyday_victory_ranking($user_id);
                                    if (!empty($victory_ranking)) {
                                        foreach ($victory_ranking as $k=>$v){
                                            for ($i=0;$i<count($prize);$i++){
                                                if($v['rank'] >= $prize[$i]['min_rank'] && $v['rank'] <= $prize[$i]['max_rank']){
                                                    if ($prize[$i]['prize_type'] == 1){
                                                        $name = '房卡';
                                                    }
                                                    if ($prize[$i]['prize_type'] == 2){
                                                        $name = '现金';
                                                    }
                                                    if ($prize[$i]['prize_type'] == 3){
                                                        $name = '其他';
                                                    }
                                                    $victory_ranking['prize'] = $name;
                                                    break;
                                                }else{
                                                    $victory_ranking['prize'] = '暂无奖励';
                                                }
                                            }
                                        }
                                        $data = $this->player_model->get_all_user_everyday_victory_ranking();
                                        foreach ($data as $key => $v) {
                                            if ($v['rank'] == ($victory_ranking['rank'] - 1)) {
                                                $res = $v;
                                            }
                                        }
                                        $victory_ranking['differ'] = $res['num'] - $victory_ranking['num'];
                                    }else{
                                        $victory_ranking['prize'] = '暂无奖励';
                                        $victory_ranking['num'] = 0;
                                        $victory_ranking['rank'] = 0;
                                        $victory_ranking['differ'] = 0;
                                    }
                                    $victory_ranking['time'] = date('Y-m-d',time());

                                    $activity_setting['victory_rule'] = $value['activity_rule'];
                                    $activity_setting['switch_victory_ranking'] = true;
                                    $activity_setting['victory_title'] = $value['activities_title'];
                                }else
                if($value && $value['send_type'] == 2){
                    $victory_ranking = $this->player_model->get_victory_inning_one_user_ranking($user_id,date('Y-m-d',$value['begin_time']),date('Y-m-d',$value['end_time']));
                    if (!empty($victory_ranking)) {
                        if (!empty($prize)){
                            for ($i=0;$i<count($prize);$i++){
                                if($victory_ranking['rank'] >= $prize[$i]['min_rank'] && $victory_ranking['rank'] <= $prize[$i]['max_rank']){
                                    if ($prize[$i]['prize_type'] == 1){
                                        $name = $prize[$i]['prize_num'];
                                    }
                                    if ($prize[$i]['prize_type'] == 2){
                                        $name = $prize[$i]['prize_num'];
                                    }
                                    if ($prize[$i]['prize_type'] == 3){
                                        $name = $prize[$i]['prize_num'];
                                    }
                                    $victory_ranking['prize'] = $name;
                                    break;
                                }else{
                                    $victory_ranking['prize'] = '暂无奖励';
                                }
                            }
                            $data = $this->player_model->get_victory_inning_all_user_ranking(date('Y-m-d', $value['begin_time']), date('Y-m-d', $value['end_time']));
                            if ($victory_ranking['rank'] == 1){
                                $victory_ranking['differ'] = 0;
                            }else{
                                foreach ($data as $key => $v) {
                                    if ($v['rank'] == ($victory_ranking['rank'] - 1)) {
                                        $res = $v;
                                    }
                                }
                                $victory_ranking['differ'] = $res['num'] - $victory_ranking['num'];
                            }
                        }else{
                            $victory_ranking['prize'] = '暂无奖励';
                        }
                    }else{
                        $victory_ranking['prize'] = '暂无奖励';
                        $victory_ranking['num'] = 0;
                        $victory_ranking['rank'] = 0;
                        $victory_ranking['differ'] = 0;
                    }
                    $victory_ranking['time'] = date('Y-m-d',$value['begin_time']).'    '.'至'.date('m-d',time());

                    $activity_setting['victory_rule'] = $value['activity_rule'];
                    $activity_setting['switch_victory_ranking'] = true;
                    $activity_setting['victory_title'] = $value['activities_title'];
                }
            }
        }

        $data['victory_ranking'] = $victory_ranking;
        $data['bureau_ranking'] = $bureau_ranking;
        $data['activity_setting'] = $activity_setting;
        $data['scoring_record'] = $scoring_record;
        return $data;
    }

    //签到
    public function signIn()
    {
        $user_id = $this->input->post('user_id');
        $game_id = $this->input->post('game_id');
        $data = $this->mp_model->get_mp_one_activity_setting($game_id,$id = 1);
        if (!empty($data['bonus_point'])){
            $num = $data['bonus_point'];//签到获得多多分
        }else{
            $num = 10;//签到获得多多分
        }

        $this->player_model->set_database($game_id);

        $user = $this->player_model->get_role_info($user_id);

        if (!empty($user)) {
            $record = $this->player_model->get_user_signin_record($user_id,$game_id);
            if (count($record) < 1){
                //添加多多分
                $res = $this->player_model->update_role_DDScore($user_id,$num,$style = 1);
                if ($res) {
                    $this->player_model->insert_user_signin_record($user_id,$game_id);
                    $score = $this->player_model->get_role_score($user_id);
                    echo json_encode(array('status' => true,'score'=>$score,'is_signin'=>true, 'msg' => '签到成功'));
                } else {
                    echo json_encode(array('status' => false, 'is_signin'=>false, 'msg' => '签到失败'));
                }
            }else{
                echo json_encode(array('status' => false, 'is_signin'=>false, 'msg' => '你今天已经签到过了哦,请勿重复签到哦!'));
            }
        }else{
            echo json_encode(array('status' => false,'msg' => '用户不存在'));
        }
    }

    //兑换物品
    public function exchangeGoods()
    {
        $role_id = $this->input->post('user_id');
        $goods_id = $this->input->post('goods_id');
        $game_id = $this->input->post('game_id');
        $this->player_model->set_database($game_id);

        //获取用户信息
        $user = $this->player_model->get_role_info($role_id);
        //获取用户分
        $score = $this->player_model->get_role_score($role_id);
        //获取商品信息
        $goods = $this->db->get_where('mp_exchange_goods', array('goods_id' => $goods_id))->row_array();
        if (!empty($user) && !empty($goods)) {
            if ($score['DDScore'] >= $goods['dd_score']) {
                if ($goods['limit_type'] == 1){
                    //获取用户每天兑换记录
                    $everyday = $this->player_model->get_user_everyday_exchange($role_id,$goods_id);
                    if (count($everyday) >= $goods['limit_num']){
                        echo json_encode(array('status' => false,'is_send'=>0, 'msg' => '每日只能兑换'.$goods['limit_num'].'次哦'));
                        exit();
                    }
                }else if ($goods['limit_type'] == 2){
                    //获取用户活动周期兑换记录
                    $activity = $this->player_model->get_user_activity_exchange($role_id,$goods_id,date('Y-m-d',$goods['begin_time']),date('Y-m-d',$goods['end_time']));
                    if (count($activity) >= $goods['limit_num']){
                        echo json_encode(array('status' => false,'is_send'=>0,'msg' => date('Y-m-d',$goods['begin_time']).'至'.date('m-d',$goods['end_time']).' 期间'.'只能兑换'.$goods['limit_num'].'次'));
                        exit();
                    }
                }
                //添加兑换记录
                $id = $this->mp_model->insert_mp_exchange_record($role_id, $goods_id);
                if (empty(!$id)) {
                    //给用户添加房卡
                    if ($goods['goods_type'] == 1) {
                        //发送房卡
                        $result = $this->player_model->update_role_score($role_id, $goods['goods_num']);
                        //添加成功
                        if ($result) {
                            //更新用户多多分
                            $this->player_model->update_role_DDScore($role_id, $goods['dd_score'],$style = 0);
                            $this->mp_model->update_exchange_record($id,array('exchange_status'=>1));
                            $score = $this->player_model->get_role_score($role_id);
                            echo json_encode(array('status' => true,'is_send'=>1, 'msg' => '兑换成功','score'=>$score));
                            exit();
                        }else{
                            $this->mp_model->update_exchange_record($id,array('exchange_status'=>0));
                            echo json_encode(array('status' => true,'is_send'=>0, 'msg' => '兑换失败'));
                            exit();
                        }
                    } else if ($goods['goods_type'] == 2) {
                        //给用户发微信红包
                        $result = $this->Send_red_packet($role_id,$game_id,intval($goods['goods_num']));
                        if ($result){
                            $this->player_model->update_role_DDScore($role_id, $goods['dd_score'],$style = 0);
                            $this->mp_model->update_exchange_record($id,array('exchange_status'=>1));
                            $score = $this->player_model->get_role_score($role_id);
                            echo json_encode(array('status' => true, 'is_send'=>1, 'msg' => '红包发送成功','score'=>$score));
                            exit();
                        }else{
                            $this->mp_model->update_exchange_record($id,array('exchange_status'=>0));
                            log_message('error','现金红包');
                            log_message('error',$result);
                            echo json_encode(array('status' => true,'is_send'=>0, 'msg' => '红包发送失败'));
                            exit();
                        }
                    } else if ($goods['goods_type'] == 3) {
                        $res = $this->player_model->update_role_DDScore($role_id, $goods['dd_score'],$style = 0);
                        if ($res){
                            $this->mp_model->update_exchange_record($id,array('exchange_status'=>1));
                            //通知用户填写地址
                            $score = $this->player_model->get_role_score($role_id);
                            echo json_encode(array('status' => false,'is_send'=>2,'record_id'=>$id,'msg' => '兑换成功,请填写你的地址','score'=>$score));
                            exit();
                        }else{
                            $this->mp_model->update_exchange_record($id,array('exchange_status'=>0));
                            echo json_encode(array('status' => false,'is_send'=>0,'msg' => '兑换失败'));
                            exit();
                        }
                    }
                } else {
                    echo json_encode(array('status' => false,'msg' => '插入记录失败'));
                    exit();
                }
            }else{
                echo json_encode(array('status' => false,'is_send'=>0, 'msg' => '你的多多分不够'));
            }
        }else{
            echo json_encode(array('status' => false, 'msg' => '商品不存在'));
            exit();
        }
    }

    //实物地址
    public function exchangeAddress()
    {
        $record_id = $this->input->post('id');
        $data['address'] = $this->input->post('address');
        $data['name'] = $this->input->post('name');
        $data['phone'] = $this->input->post('phone');

        if (empty($data['address'])){
            echo json_encode(array('status' => false, 'is_add'=>0 ,'msg' => '地址不能为空'));
            exit();
        }
        if (empty($data['name'])){
            echo json_encode(array('status' => false, 'is_add'=>0 ,'msg' => '名称不能为空'));
            exit();
        }
        if (empty($data['phone'])){
            echo json_encode(array('status' => false, 'is_add'=>0 ,'msg' => '号码不能为空'));
            exit();
        }
        $res = $this->mp_model->update_exchange_record($record_id,$data);

        if ($res){
            echo json_encode(array('status' => false, 'is_add'=>1 ,'msg' => '兑换成功'));
            exit();
        }else{
            echo json_encode(array('status' => false, 'is_add'=>0 ,'msg' => '添加失败,请重试'));
            exit();
        }
    }

    //发送现金红包
    public function Send_red_packet($user_id,$game_id,$money)
    {
        $money = intval($money);
        //查找用户
        $this->db->where('role_id',$user_id);
        $this->db->where('game_id',$game_id);
        $res = $this->db->get('mp_user')->row_array();

        if(empty($res) || empty($res['openid'])) {
            $error['code'] = 0;
            $error['info'] = '失败,未在公众号内注册过';
            return false;
            exit();
        }

        $mp_config = $this->mp_model->get_mp_config($game_id);
        WxPayConfig::setConfig($mp_config);
        $game_info = $this->mp_model->get_game_info($game_id);

        $this->load->helper('string');
        $order_no = date("YmdHis") . random_string('nozero', 3);

        // 创建提现订单
        $data = array(
            'role_id' => $res['role_id'],
            'role_name'  => $res['nickname'],
            'order_no' => $order_no,
            'total_fee' => $money,
            'create_time' => time(),
            'game_id'=>$game_id
        );

        $this->db->insert('mp_exchange_cash', $data);

        $id = $this->db->insert_id();

        if ($id){
            $input = new WxPaySendredpack();
            $input->SetMch_billno($order_no);//商户订单号
            $input->SetSend_name($game_info['game_name']);//商户名称
            $input->SetRe_openid($res['openid']);//用户openid
            $input->SetTotal_amount($money * 100);//付款金额
            $input->SetTotal_num(1);//红包发放总人数
            $input->SetWishing($game_info['game_name']);//红包祝福语
            $input->SetAct_name($game_info['game_name']);//活动名称
            $input->SetRemark($game_info['game_name']);//备注信息
            if ($money >= 200){
                $input->SetScene_id('PRODUCT_3');//场景id
            }
            $order = WxPayApi::SendredpackOrder($input);

            if ($order['return_code'] == 'SUCCESS' && $order['result_code'] == 'SUCCESS'){
                $this->db->where('id',$id);
                $this->db->update('mp_exchange_cash',array('out_trade_no'=>$order['send_listid'],'status'=>1));

                return true;
            }else{
                $this->db->where('id',$id);
                $this->db->update('mp_exchange_cash',array('status'=>-1));

                return false;
            }
        }else{
            return false;
        }
    }*/

    // 跳转
    public function redirect() {

        $url = $this->input->get('url');
        $game_id = $this->input->get('game_id');

        $mp_config = $this->mp_model->get_mp_config($game_id);

        $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$mp_config['appid'].'&redirect_uri='.urlencode($url).'&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect';

        Header("Location: $url");
    }

    public function redirect2() {

        $url = $this->input->get('url');
        $channel_id = $this->input->get('channel_id');
        $role_id = $this->input->get('role_id');

//        echo $url;exit;

        $url .= '?channel_id='.$channel_id.'&role_id='.$role_id;

        $channel = $this->server_model->get_one_channel($channel_id);

        $mp_config = $this->mp_model->get_mp_config($channel['game_id']);

        $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$mp_config['appid'].'&redirect_uri='.urlencode($url).'&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect';
//echo $url;
        Header("Location: $url");
    }

    public function h5() {
        $url = $this->input->get('url');
//        echo $url;
//        Header("Location: $url");exit;
        echo ("<script>location.href='$url'</script>");exit;
    }

    public function h5_result() {
        $channel_id = $this->input->get('channel_id');

        $this->load->model('server_model');


        $channel = $this->server_model->get_one_channel($channel_id);
        $game = $this->server_model->get_game_by_id($channel['game_id']);

        $system = "unkonw";

        if(strpos($_SERVER['HTTP_USER_AGENT'], 'iPhone')||strpos($_SERVER['HTTP_USER_AGENT'], 'iPad')){
            $system = "ios";
        }else if(strpos($_SERVER['HTTP_USER_AGENT'], 'Android')){
            $system = "android";
        }

        if($system == 'ios') {
            $this->db->where('game_id',$channel['game_id']);
            $this->db->where_in('platform',array(2,3));
            $this->db->where('is_online',1);
            $query = $this->db->get('channel');
            $channel = $query->row_array();
        } else {
            // 获取对应的安卓渠道
            $channels  = $this->server_model->get_game_channels($channel['game_id'],1);
            if(count($channels)>0) {
                $channel = $channels[0];
            }
        }

        $url = $game['magicwindow_url'];

        echo ("<script>location.href='$url'</script>");exit;
    }

    // 下载
    public function down() {
        $game_id = $this->input->get('game_id');

        $this->db->where('key','menu_download');
        $this->db->where('game_id',$game_id);
        $query = $this->db->get('mp_menu');
        $row = $query->row_array();

        $url = $row['url'];

        Header("Location: $url");
    }

    // 公众号支付
    public function createOrder()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');
        //        header("Content-type: application/json");

        log_message("error", "获取支付请求");
        log_message("error", http_build_query($_POST));

        $good_id = $this->input->post('good_id');
        $role_id = $this->input->post('role_id');
        $game_id = $this->input->post('game_id');
        $seller_id = $this->input->post('seller_id');

        $this->load->model('user_model');
        $mp_config = $this->mp_model->get_mp_config($game_id);
        $mp_user = $this->mp_model->get_mp_user_by_roleid($role_id,$game_id);
        $this->player_model->set_database($game_id);
        $role = $this->player_model->get_role_info($role_id);

        if(!$mp_user) {
            echo json_encode(array('status' => false, 'msg' => '用户不存在'));
            exit;
        }

        // 是否为代理
        $agent = $this->user_model->get_one_agent($role_id,$game_id);

        //获取推荐玩家数
        $recommend_num = $this->user_model->get_one_recommend_player($role_id);

        // 获取商品信息
        $good = $this->mp_model->get_mp_good_by_id($good_id);

        if(empty($good)) {
            echo json_encode(array('status' => false, 'msg' => '商品不存在'));
            exit;
        }

        if($good['type']==1) { // 普通玩家购买
            if($agent) {
                if($recommend_num >= 5) {
                    echo json_encode(array('status' => false, 'msg' => '商品信息不合法'));
                    exit;
                }
            }
        }/* else {
            if(!$agent && !in_array($game_id,array(30))) {
                echo json_encode(array('status' => false, 'msg' => '商品信息不合法'));
                exit;
            }
        }*/


        $money = $good['price'];

//        $money = 0.01;

        $this->load->helper('string');

        $order_no = date("YmdHis") . random_string('nozero', 3);

        // 创建订单
        $data = array(
            'user_id' => $mp_user['user_id'],
            'role_id'  => $mp_user['role_id'],
            'agent_id'  => $agent?$agent['id']:0,
            'good_id'  => $good_id,
            'game_id'  => $game_id,
            'order_no' => $order_no,
            'is_first' => $good['is_first'],
            'is_agent' => ($good['type']-1),
            'total_fee' => $money,
            'kind_id'  =>$role['CommonKindID'],
            'create_time' => time()
        );

        // 大盘山游戏采用代理充值方式
        if(in_array($game_id,array(15))) {
            if($seller_id) {
                $agent2 = $this->user_model->get_agent_by_id($seller_id);

                if(empty($agent2)) {
                    echo json_encode(array('status' => false, 'msg' => '代理信息不存在'));
                    exit;
                }

                if($agent2['score'] < $good['real_score']) {
                    echo json_encode(array('status' => false,'is_stock'=>true, 'msg' => '代理库存不足，请联系代理'));
                    exit;
                }

                $data['seller_id'] = $seller_id;
            }

        }


        $this->db->insert('mp_order', $data);

        if ($order_id = $this->db->insert_id() > 0) {
            WxPayConfig::setConfig($mp_config);

            //②、统一下单
            $input = new WxPayUnifiedOrder();
            $input->SetAppid($mp_config['appid']);
            $input->SetBody($money . '元');
            $input->SetAttach($order_id);
            $input->SetOut_trade_no($order_no);
            $input->SetTotal_fee($money * 100);
            $input->SetTime_start(date("YmdHis"));
            $input->SetTime_expire(date("YmdHis", time() + 600));
            $input->SetGoods_tag("");
            $input->SetNotify_url("https://lhmj.tuo3.com.cn/admin/index.php/api/v1/weixin/notify");
            $input->SetTrade_type("JSAPI");
            $input->SetOpenid($mp_user['openid']);
            $order = WxPayApi::unifiedOrder($input);

            if ($order['return_code'] == 'SUCCESS' && $order['result_code'] == 'SUCCESS') {
                $tools = new JsApiPay();
                $jsApiParameters = $tools->GetJsApiParameters($order);
                log_message("error", var_export($jsApiParameters, TRUE));
                echo json_encode(array('status' => true, 'msg' => '', 'data' => $jsApiParameters, 'order' => $order_no));
                exit;
            } else {
                echo json_encode(array('status' => false, 'msg' => $order['return_msg']));
                exit;
            }
        } else {
            echo json_encode(array('status' => false, 'msg' => '订单创建失败'));
            exit;
        }
    }

    /**
     * 代充
     *
     */
    public function createOrder2()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');
        //        header("Content-type: application/json");

        log_message("error", "获取支付请求");
        log_message("error", http_build_query($_POST));

        $good_id = $this->input->post('good_id');
        $role_id = $this->input->post('role_id');
        $game_id = $this->input->post('game_id');
        $source_id = $this->input->post('source_id');

//        if($game_id == 37) {
//            echo json_encode(array('status' => false, 'msg' => '暂未开放'));
//            exit;
//        }

        $this->load->model('user_model');
        $mp_config = $this->mp_model->get_mp_config($game_id);
        $mp_user = $this->mp_model->get_mp_user_by_roleid($source_id,$game_id);
        $this->player_model->set_database($game_id);
        $role = $this->player_model->get_role_info($role_id);

        if(!$mp_user) {
            echo json_encode(array('status' => false, 'msg' => '未注册'));
            exit;
        }

        // 是否为代理
        $agent = $this->user_model->get_one_agent($role_id,$game_id);


        if($agent&&$game_id==37) {
            echo json_encode(array('status' => false, 'msg' => '不能充值'));
            exit;
        }

        $agent2 = $this->user_model->get_one_agent($source_id,$game_id);

        if($agent2&&$game_id==37) {
            echo json_encode(array('status' => false, 'msg' => '不能充值'));
            exit;
        }

        // 获取商品信息
        $good = $this->mp_model->get_mp_good_by_id($good_id);

        if(empty($good)) {
            echo json_encode(array('status' => false, 'msg' => '商品不存在'));
            exit;
        }

        $money = $good['price'];

//        $money = 0.01;

        $this->load->helper('string');

        $order_no = date("YmdHis") . random_string('nozero', 3);

        // 创建订单
        $data = array(
            'user_id' => $mp_user['user_id'],
            'role_id'  => $role_id,
            'agent_id'  => $agent?$agent['id']:0,
            'good_id'  => $good_id,
            'game_id'  => $game_id,
            'order_no' => $order_no,
            'is_first' => $good['is_first'],
            'source_id' => $source_id,
            'is_agent' => ($good['type']-1),
            'total_fee' => $money,
            'kind_id'  =>$role['CommonKindID'],
            'create_time' => time()
        );

        $this->db->insert('mp_order', $data);

        if ($order_id = $this->db->insert_id() > 0) {
            WxPayConfig::setConfig($mp_config);

            //②、统一下单
            $input = new WxPayUnifiedOrder();
            $input->SetAppid($mp_config['appid']);
            $input->SetBody($money . '元');
            $input->SetAttach($order_id);
            $input->SetOut_trade_no($order_no);
            $input->SetTotal_fee($money * 100);
            $input->SetTime_start(date("YmdHis"));
            $input->SetTime_expire(date("YmdHis", time() + 600));
            $input->SetGoods_tag("");
            $input->SetNotify_url("https://lhmj.tuo3.com.cn/admin/index.php/api/v1/weixin/notify");
            $input->SetTrade_type("JSAPI");
            $input->SetOpenid($mp_user['openid']);
            $order = WxPayApi::unifiedOrder($input);

            if ($order['return_code'] == 'SUCCESS' && $order['result_code'] == 'SUCCESS') {
                $tools = new JsApiPay();
                $jsApiParameters = $tools->GetJsApiParameters($order);
                log_message("error", var_export($jsApiParameters, TRUE));
                echo json_encode(array('status' => true, 'msg' => '', 'data' => $jsApiParameters, 'order' => $order_no));
                exit;
            } else {
                echo json_encode(array('status' => false, 'msg' => $order['return_msg']));
                exit;
            }
        } else {
            echo json_encode(array('status' => false, 'msg' => '订单创建失败'));
            exit;
        }
    }

    public function notify() {
        $post_data = file_get_contents('php://input');

        log_message("error","微信通知结果");
        log_message("error",$post_data);

        header('Content-Type: application/xml');
        $response = new SimpleXMLElement('<xml></xml>');

        $this->load->model('user_model');

        if(!$post_data){
            // http_response_code(500);
            $response->addChild('return_code', 'FAIL');
            $response->addChild('return_msg', '参数非法');
            echo $response->asXML();exit;
            // echo json_encode(array('code'=>'FAIL','message'=>'参数非法'));exit;
        }
        //将XML转为array
        //禁止引用外部xml实体
        libxml_disable_entity_loader(true);
        $data = json_decode(json_encode(simplexml_load_string($post_data, 'SimpleXMLElement', LIBXML_NOCDATA)), true);

        if($data['return_code'] == 'SUCCESS') {

            $money = $data['cash_fee'];
            $out_trade_no = $data['out_trade_no'];
            $transaction_id = $data['transaction_id'];
            $bank_type = $data['bank_type'];

            // 获取订单
            $this->db->where('order_no',$out_trade_no);
            $query = $this->db->get('mp_order');
            $order = $query->row_array();

            if($order) {
                if(!empty($order['transaction_id'])&&$order['status']!=0) {
                    // http_response_code(204);
                    $response->addChild('return_code', 'SUCCESS');
                    $response->addChild('return_msg', '订单已交易');
                    echo $response->asXML();exit;
                    // echo json_encode(array('code'=>'SUCCESS','message'=>'订单已交易'));
                    exit;
                } else {
                    $this->load->model('player_model');
                    $this->player_model->set_database($order['game_id']);

                    if($data['result_code'] == 'SUCCESS') {

                        $game_id = $order['game_id'];

                        // 获取商品信息
                        $good = $this->mp_model->get_mp_good_by_id($order['good_id']);


                        $this->load->model('player_model');
                        $this->player_model->set_database($order['game_id']);
                        $role = $this->player_model->get_role_info($order['role_id']);

//                        if($order['total_fee']*100 == $money) {

                        $update_data = array('status'=>1,
                            'transaction_id'=>$transaction_id,
                            'bank_type'=>$bank_type,
                            'spreader_id'=>$role['SpreaderID'],
                            'pay_time'=>time());

                        if($this->mp_model->update_mp_order($order['id'],$update_data)) {

                            // 查询是否为代理
                            $adminer = $this->user_model->get_one_agent($order['role_id'],$game_id);
                            if($adminer) {

                                if (in_array($game_id,array(37,55))){
                                    $this->user_model->update_agent_score($adminer['id'],$good['score']);
                                }else{
                                    $this->player_model->update_role_score($order['role_id'],$good['score']);
                                }

                                if($good['is_month_card'] == 1) {
                                    $this->player_model->insert_month_card($order['role_id'],$good['month_card_type']);
                                }

                                // 计算代理收益
                                if($game_id != 9 && $adminer['spreader_id']>0) {
                                    // 获取上级代理
                                    $parent = $this->user_model->get_agent_by_id($adminer['spreader_id']);
                                    // 判断其下级代理个数
                                    $count = count($this->user_model->get_children_agent($adminer['spreader_id'],1));

                                    $rate = 0.00;

                                    if($count<3) {
                                        $rate = 0.08;
                                    } else {
                                        $rate = 0.15;
                                    }

/*                                    if(in_array($game_id,array(16,17))) {
                                        $rate = 0.5;
                                    }*/

                                    if(in_array($game_id,array(20))) {
                                        $rate = 0.5;
                                    }

                                    if(in_array($game_id,array(30))) {
                                        $rate = 0.376;
                                    }

                                    if(in_array($game_id,array(32,36,54))) {
                                        $rate = 0.5;
                                    }

                                    if(in_array($game_id,array(35))) {
                                        $rate = 0.45;
                                    }

                                    if(in_array($game_id,array(37))){
                                        $rate = 0.15;
                                    }

                                    // 更新收益
                                    $this->user_model->update_agent_bonus($parent['id'],$rate*$order['total_fee']);
$file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n111---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                    // 获取上上级代理
                                    if($parent['spreader_id']>0) {
                                        $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                                        // 判断其下级代理个数
                                        $count2 = count($this->user_model->get_children_agent($parent['spreader_id'],1));

                                        if(in_array($game_id,array(16,17))) {

                                            $rate = 0.1;

                                            if($count2 >=3) { //享受2级代理收益
                                                $rate = 0.2;
                                            }

                                            $this->user_model->update_agent_bonus($parent2['id'],$rate*$order['total_fee']);
$file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n222---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                            if($parent2['spreader_id']>0) {
                                                // 获取上上级代理
                                                $parent3 = $this->user_model->get_agent_by_id($parent2['spreader_id']);
                                                // 判断其下级代理个数
                                                $count3 = count($this->user_model->get_children_agent($parent2['spreader_id'],1));

                                                if($count3>=5) { // 享受3级代理收益
                                                    $rate = 0.1;

                                                    $this->user_model->update_agent_bonus($parent3['id'],$rate*$order['total_fee']);
$file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n333---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                                }

                                            }

                                        } else {
/*                                            if($count2>=5) { //享受2级代理收益
                                                $rate = 0.1;

                                                $this->user_model->update_agent_bonus($parent2['id'],$rate*$order['total_fee']);
                                            }*/

                                            if (in_array($game_id,array(30))){
                                                $rate = 0.047;

                                                $this->user_model->update_agent_bonus($parent2['id'],$rate*$order['total_fee']);
$file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n444---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                            }

                                            if (in_array($game_id,array(20,32,35))){
                                                $rate = 0.06;

                                                if($game_id == 20) {
//                                                    if($parent['district_id']==1 && $parent2['district_id']==1) {
                                                        $this->user_model->update_agent_bonus($parent2['id'],$rate*$order['total_fee']);
//                                                    }
//                                                    if($parent['district_id'] == 10 && $parent2['district_id'] == 10) {
//                                                        if($parent2['one_level_bonus'] == 1) {
//                                                            $rate = 0.05;
//                                                            $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);
//                                                        }
//                                                    }
 $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n20---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                                } else {
                                                    $this->user_model->update_agent_bonus($parent2['id'],$rate*$order['total_fee']);
 $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n555---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                               }

                                            }

                                            if (in_array($game_id,array(36,37,54))){
                                                $rate = 0.1;

                                                $this->user_model->update_agent_bonus($parent2['id'],$rate*$order['total_fee']);
  $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n666---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                            }

                                            if($parent2['spreader_id']>0) {
                                                // 获取上上级代理
                                                $parent3 = $this->user_model->get_agent_by_id($parent2['spreader_id']);
                                                // 判断其下级代理个数
                                                $count3 = count($this->user_model->get_children_agent($parent2['spreader_id'],1));

/*                                                if($count3>=7) { // 享受3级代理收益
                                                    $rate = 0.05;
                                                    $this->user_model->update_agent_bonus($parent3['id'],$rate*$order['total_fee']);

                                                }*/

                                                if (in_array($game_id,array(30))) {
                                                    $rate = 0.0188;

                                                    $this->user_model->update_agent_bonus($parent3['id'],$rate*$order['total_fee']);
 $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n6666---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                                }

                                                if (in_array($game_id,array(32,35))) {
                                                    $rate = 0.04;

                                                    $this->user_model->update_agent_bonus($parent3['id'],$rate*$order['total_fee']);
  $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n777---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                                }

                                                if (in_array($game_id,array(36,37,54))) {
                                                    $rate = 0.05;

                                                    $this->user_model->update_agent_bonus($parent3['id'],$rate*$order['total_fee']);
               $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n888---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                $this->load->model('player_model');
                                $this->player_model->set_database($order['game_id']);
                                $role = $this->player_model->get_role_info($order['role_id']);

                                // 获取其推荐码

                                if($role && $role['SpreaderID']>0) {
                                    // 判断推荐码是否为代理
                                    $agent = $this->user_model->get_one_agent($role['SpreaderID'],$order['game_id']);

                                    if($agent) {

                                        $rate = 0.25;

                                        if($game_id == 6) {
                                            $rate = 0.35;
                                        } else if($game_id == 7) {
                                            $rate = 0.5;
                                        } else if($game_id == 9) {
                                            $rate = 0.4;
                                        } else if($game_id == 14) {
                                            $rate = 0.2;
                                        } else if(in_array($game_id,array(20))){
                                            $rate = 0.5;
                                        }else if(in_array($game_id,array(30))){
                                            $rate = 0.376;
                                        }else if(in_array($game_id,array(32,36,54))){
                                            $rate = 0.5;
                                        }else if(in_array($game_id,array(35,37))){
                                            $rate = 0.45;
                                        }

                                        $this->user_model->update_agent_bonus($agent['id'],$rate*$order['total_fee']);
 $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n999---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                        if($game_id == 30 && $agent['spreader_id']>0) {
                                            $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);
                                            $rate = 0.047;

                                            // 更新收益
                                            $this->user_model->update_agent_bonus($parent['id'],$rate*$order['total_fee']);
 $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n10---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                            // 获取上上级代理
                                            if($parent['spreader_id']>0) {
                                                $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                                                $rate = 0.0188;
                                                $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);
  $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n11---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                            }
                                        }

                                        if(in_array($game_id,array(20,32,35)) && $agent['spreader_id']>0) {
                                            $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);
                                            $rate = 0.06;

                                            if($game_id == 20) {
                                               if($agent['district_id']==1 && $parent['district_id']==1) {
                                                   $this->user_model->update_agent_bonus($parent['id'],$rate*$order['total_fee']);
    $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n12---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                               }
                                                if(($agent['district_id'] == 3 && $parent['district_id'] == 3)
                                                    ||($agent['district_id'] == 10 && $parent['district_id'] == 10)
                                                    ||($agent['district_id'] == 11 && $parent['district_id'] == 11))
                                                {
                                                    if($parent['one_level_bonus'] == 1) {
                                                        $rate = 0.05;
                                                        $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);
$file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n13---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                                    }
                                                }
                                            } else {

                                                // 更新收益
                                                $this->user_model->update_agent_bonus($parent['id'],$rate*$order['total_fee']);

                                                // 获取上上级代理
                                                if($parent['spreader_id']>0) {
                                                    $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                                                    $rate = 0.04;
                                                    $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);
     $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n14---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                                }
                                            }

                                        }

                                        // 临海麻将
                                        if($game_id == 37 && $agent['spreader_id']>0) {
                                            // 获取上级代理
                                            $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);
                                            $rate = 0.075;

                                            // 更新收益
                                            $this->user_model->update_agent_bonus($parent['id'],$rate*$order['total_fee']);

                                            // 获取上上级代理
                                            if($parent['spreader_id']>0) {
                                                $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                                                $rate = 0.05;
                                                $this->user_model->update_agent_bonus($parent2['id'],$rate*$order['total_fee']);
    $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n15---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                            }
                                        }

                                        if(in_array($game_id,array(36,54)) && $agent['spreader_id']>0) {
                                            // 获取上级代理
                                            $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);
                                            $rate = 0.1;

                                            // 更新收益
                                            $this->user_model->update_agent_bonus($parent['id'],$rate*$order['total_fee']);
 $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n16---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                            // 获取上上级代理
                                            if($parent['spreader_id']>0) {
                                                $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                                                $rate = 0.05;
                                                $this->user_model->update_agent_bonus($parent2['id'],$rate*$order['total_fee']);
   $file22 = fopen("pay_weixin.txt","a+");
fwrite($file22, "\r\n17---".$game_id."---".date("Y-m-d H:i:s",time())."---rate=".$rate."---fee=".$rate*$order['total_fee']);
fclose($file22);
                                            }
                                        }
                                    }
                                }

                                $this->player_model->update_role_score($order['role_id'],$good['score']);

                                if($good['is_month_card'] == 1) {
                                    $this->player_model->insert_month_card($order['role_id'],$good['month_card_type']);
                                }
                            }

                            $this->player_model->proc_pay_event($order['role_id'], $order['total_fee']);
                            // http_response_code(204);    
                            // echo json_encode(array('code'=>'SUCCESS','message'=>'订单更新成功'));exit;
                            $response->addChild('return_code', 'SUCCESS');
                            $response->addChild('return_msg', '订单更新成功');
                            echo $response->asXML();exit;
                        } else {
                            // http_response_code(500);
                            // echo json_encode(array('code'=>'FAIL','message'=>'订单更新失败'));exit;
                            $response->addChild('return_code', 'FAIL');
                            $response->addChild('return_msg', '订单更新失败');
                            echo $response->asXML();exit;
                        }
                    } else {
                        // http_response_code(500);
                        // echo json_encode(array('code'=>'FAIL','message'=>'订单金额不一致'));exit;
                        $response->addChild('return_code', 'FAIL');
                        $response->addChild('return_msg', '订单金额不一致');
                        echo $response->asXML();exit;
                    }
//                    } else { // 支付失败
//                        $this->order_model->update_order($order['order_id'],array('status'=>2,'pay_error_code'=>$data['err_code'],'pay_error_msg'=>$data['err_code_des']));
//                    }
                }
            } else {
                // http_response_code(500);
                // echo json_encode(array('code'=>'FAIL','message'=>'订单不存在'));exit;
                $response->addChild('return_code', 'FAIL');
                $response->addChild('return_msg', '订单不存在');
                echo $response->asXML();exit;
            }
        } else {
            // http_response_code(500);
            // echo json_encode(array('code'=>'FAIL','message'=>$data['error_code']));exit;
            $response->addChild('return_code', 'FAIL');
            $response->addChild('return_msg', $data['error_code']);
            echo $response->asXML();exit;
            
        }
    }

    //消耗排名(新年活动)
/*    public function recharge_rank()
    {
        $game_id = $this->input->post('game_id');

        $this->player_model->set_database($game_id);

        $sql = "SELECT a.role_id,SUM(a.total_fee) AS total FROM (SELECT * FROM tuo3_mp_order WHERE `status` = 1 AND pay_time >= '1517846400' AND pay_time <= '1518105600' AND game_id = ".$game_id." ORDER BY id DESC LIMIT 10000000)AS a GROUP BY a.role_id ORDER BY total DESC,a.pay_time ASC LIMIT 10";
        $query = $this->db->query($sql);
        $rank = $query->result_array();

        foreach ($rank as $key=>$value){
            $role_info = $this->player_model->get_role_info($value['role_id']);

            $rank[$key]['username'] = empty($role_info['NickName'])?'':$role_info['NickName'];
        }

        show_response(1,'',$rank);
    }
*/

    //胜局排名(新年活动)
    public function victory_rank()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $game_id = $this->input->post('game_id');
        $role_id = $this->input->post('role_id');

        $day = $this->input->post('day');
        $mode = $this->input->post('mode');

        $day = $day?$day:date('Y-m-d');

        $this->player_model->set_database($game_id);

        $start_day = '2018-09-01';
        $end_day = '2018-09-05';


        if($mode == 'daily') {
            $rank = $this->player_model->get_played_count_rank(10,$day);
        } else {
            $rank = $this->player_model->get_played_count_rank2(10,$start_day,$end_day);
        }

        foreach ($rank as $key=>$value){
            $role_info = $this->player_model->get_role_info($value['UserID']);
            $rank[$key]['Rank'] = $key+1;
            $rank[$key]['NickName'] = empty($role_info['NickName'])?'':$role_info['NickName'];

            $prize =  '';

            if($mode == 'daily') {
                switch($key) {
                    case 0:
                        $prize = '38元红包';
                        break;
                    case 1:
                        $prize = '28元红包';
                        break;
                    case 2:
                        $prize = '18元红包';
                        break;
                    case 3:
                    case 4:
                        $prize = '10张房卡';
                        break;
                    default:
                        $prize = '5张房卡';
                        break;
                }
            } else {
                switch($key) {
                    case 0:
                        $prize = '小米手机1台';
                        break;
                    case 1:
                        $prize = '美的空调扇1台';
                        break;
                    case 2:
                        $prize = '小米双肩包1个';
                        break;
                    case 3:
                    case 4:
                        $prize = '品牌充电宝1个';
                        break;
                    default:
                        $prize = '50张房卡';
                        break;
                }
            }

            $rank[$key]['Prize'] = $prize;
        }

        $my['today_count'] = $this->player_model->get_played_count_rank_by_roleid($role_id,$day);
        $my['total_count'] = $this->player_model->get_played_count_rank_by_roleid2($role_id,$start_day,$end_day);


        show_response(1,'',array('rank'=>$rank,'day'=>$day,'my'=>$my));
    }

    public function victory_rank2()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $game_id = $this->input->get('game_id');
        $role_id = $this->input->get('role_id');

        $day = $this->input->get('day');
        $mode = 'daily';

        $day = $day?$day:date('Y-m-d');

        $this->player_model->set_database($game_id);

        $start_day = '2018-08-01';
        $end_day = '2018-08-10';


        if($mode == 'daily') {
            $rank = $this->player_model->get_played_count_rank(10,$day);
        } else {
            $rank = $this->player_model->get_played_count_rank2(10,$start_day,$end_day);
        }

        $str =  "<table><tbody><tr><th>名次</th><th>ID</th><th>角色</th><th>局数</th><th>奖品</th></tr>";


        foreach ($rank as $key=>$value){
            $role_info = $this->player_model->get_role_info($value['UserID']);
            $rank[$key]['Rank'] = $key+1;
            $rank[$key]['NickName'] = empty($role_info['NickName'])?'':$role_info['NickName'];

            $prize =  '';

            if($mode == 'daily') {
                switch($key) {
                    case 0:
                        $prize = '66元红包';
                        break;
                    case 1:
                        $prize = '44元红包';
                        break;
                    case 2:
                        $prize = '22元红包';
                        break;
                    default:
                        $prize = '10张房卡';
                        break;
                }
            } else {
                switch($key) {
                    case 0:
                        $prize = '小米手机1台';
                        break;
                    case 1:
                        $prize = '美的空调扇1台';
                        break;
                    case 2:
                        $prize = '小米双肩包1个';
                        break;
                    case 3:
                    case 4:
                        $prize = '品牌充电宝1个';
                        break;
                    default:
                        $prize = '50张房卡';
                        break;
                }
            }

            $rank[$key]['Prize'] = $prize;

            $str .= '<tr><td>'.($key+1).'</td><td>'.$value['UserID'].'</td><td>'.$role_info['NickName'].'</td><td>'.$value['PlayedCount'].'</td><td>'.$prize.'</td></tr>';
        }

        $my['today_count'] = $this->player_model->get_played_count_rank_by_roleid($role_id,$day);
        $my['total_count'] = $this->player_model->get_played_count_rank_by_roleid2($role_id,$start_day,$end_day);

        $str .= '</tbody></table>';

        echo $str;

    }


    //微信登录
    public function wechat_login($code,$game_id)
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $this->load->library('CI_Wechat', array('game_id' => $game_id));

        $this->load->model('user_model');
        $this->load->model('player_model');
        $this->player_model->set_database($game_id);

        // 获取TOKEN
        $token = $this->ci_wechat->getOauthAccessToken($code);
        log_message("error", "获取access token");
        log_message("error", var_export($token, TRUE));

        if (!$token) {
            log_message("error", $this->ci_wechat->errCode);
            log_message("error", $this->ci_wechat->errMsg);
            echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg, 'is_bind' => false, 'is_agent' => false));
            exit;
        }

        // 首先查询本地
        $user = $this->mp_model->get_mp_user_by_openid($token['openid']);

        // 未注册
        if (!$user) {
            $user = $this->ci_wechat->getOauthUserinfo($token['access_token'], $token['openid']);
            log_message("error", "获取用户信息");
            log_message("error", var_export($user, TRUE));

            if (!$user) {
                log_message("error", $this->ci_wechat->errMsg);
                log_message("error", $this->ci_wechat->errCode);
                echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg, 'is_bind' => false, 'is_agent' => false));
                exit;
            } else {
                $user['reg_time'] = time();
                $user['role_id'] = 0;
                $user['game_id'] = $game_id;

                unset($user['privilege']);
                $this->db->insert('mp_user', $user);
            }
        }

        if ($user['role_id'] > 0) {
            // 已绑定
            $role = $this->player_model->get_role_info($user['role_id']);
        } else {
            // 未绑定
            $role = $this->player_model->get_role_info_by_useruin($user['unionid']);

            if ($role) {
                $this->db->where('openid', $token['openid']);
                $this->db->update('mp_user', array('role_id' => $role['UserID']));
            }
        }

        return $role;
    }

    //抽奖算法
/*    function lottery($proArr) {
        $result = array();
        foreach ($proArr as $key => $val) {
            $arr[$key] = $val['rate'];
        }
        // 概率数组的总概率
        $proSum = array_sum($arr);
        if (empty($proSum)){
            return false;
        }
        asort($arr);
        // 概率数组循环
        foreach ($arr as $k => $v) {
            $randNum = mt_rand(1, $proSum);
            if ($randNum <= $v) {
                $result = $proArr[$k];
                break;
            } else {
                $proSum -= $v;
            }
        }
        return $result;
    }*/

    //获取微信用户信息
    public function get_wechat_user_info()
    {
        $code = $this->input->post('code');
        $game_id = $this->input->post('game_id');
        $role_id = $this->input->post('role_id');//被点赞人ID

        $this->player_model->set_database($game_id);
        $b_role = $this->player_model->get_role_info($role_id);

        if(empty($b_role)){
            echo json_encode(array('is_bind'=>false ,'msg'=>'未查询到您要点赞人的信息,请联系客服'));exit;
        }

        if(empty($b_role['UserUin'])){
            echo json_encode(array('is_bind'=>false ,'msg'=>'未查询到您要点赞人的信息,请联系客服'));exit;
        }

        $this->load->library('CI_Wechat', array('game_id' => $game_id));
        $token = $this->ci_wechat->getOauthAccessToken($code);
        $user = $this->ci_wechat->getOauthUserinfo($token['access_token'], $token['openid']);

        if(empty($token)){
            echo json_encode(array('is_bind'=>false ,'msg' =>'请允许微信公众号获取你的信息'));
        }

        if(empty($user)){
            echo json_encode(array('is_bind'=>false ,'msg' =>'请允许微信公众号获取你的信息'));
        }

        $collect_num = $this->mp_model->get_b_id_collect_num($b_role['UserUin'],$game_id);

        $draw_num = $this->mp_model->get_draw_num($b_role['UserUin'],$game_id);

        echo json_encode(array('is_bind'=>true ,'msg' =>'','user'=>array('a_id'=>$user['unionid'],'collect_num'=>$collect_num,'b_id'=>trim($b_role['UserUin']),'draw_num'=>$draw_num,'b_role_id'=>$role_id)));exit();
    }

    //点赞(新年活动)
/*    public function collect()
    {
        $game_id = $this->input->post('game_id');
        $a_id = $this->input->post('a_id');
        $b_id = $this->input->post('b_id');

        $this->player_model->set_database($game_id);

        $b_role = $this->player_model->get_role_info_by_useruin($b_id);

        if(date('Y-m-d',time()) > '2018-02-21'){
            echo json_encode(array('code'=>0,'msg'=>'活动已经结束了哦!'));exit;
        }

        if(empty($a_id)){
            echo json_encode(array('code'=>0,'msg'=>'点赞失败,请联系客服'));exit;
        }

        if(!$b_role){
            echo json_encode(array('code'=>0,'msg'=>'未查询到你点赞人的信息'));exit;
        }

        if($a_id == $b_id){
            echo json_encode(array('code'=>0,'msg'=>'不能给自己点赞哦!'));exit;
        }

        $num = $this->mp_model->get_a_id_collect_num($a_id,$b_id,$game_id);

        if($num >= 1){
            echo json_encode(array('code'=>0,'msg'=>'只能给他点赞一次哦!'));exit;
        }

        $data = $this->mp_model->insert_collect_logs(array(
            'a_id'=>$a_id,
            'b_id'=>$b_id,
            'game_id'=>$game_id,
            'insert_time'=>time()
        ));

        $num = $this->mp_model->get_b_id_collect_num($b_id,$game_id);

        if($data){
            echo json_encode(array('code'=>1,'msg'=>'点赞成功!','data'=>array('collect_num'=>$num)));exit;
        }else{
            echo json_encode(array('code'=>0,'msg'=>'点赞失败,请联系客服。'));exit;
        }
    }

    //领取(新年活动)
    public function draw()
    {
        $b_id = $this->input->post('b_id');
        $game_id = $this->input->post('game_id');

        $this->player_model->set_database($game_id);

        $b_role = $this->player_model->get_role_info_by_useruin($b_id);

        if(!$b_role){
            echo json_encode(array('code'=>0,'msg'=>'未查询到你点赞人的信息'));exit;
        }

        $collect_num = $this->mp_model->get_b_id_collect_num($b_id,$game_id);

        $draw_num = $this->mp_model->get_draw_num($b_id,$game_id);

        if($draw_num >= 1){
            echo json_encode(array('code'=>0,'msg'=>'你已经领取了哦!'));exit;
        }

        if($collect_num >= 188){

            $num = mt_rand(20,50);

            $b_role = $this->player_model->get_role_info_by_useruin($b_id);
            $result = $this->player_model->update_role_score($b_role['UserID'],$num);
            if($result){

                log_message("error",'集赞领奖');
                log_message("error", var_export(array('role_id'=>$b_role['UserID'],'num'=>$num), TRUE));

                $this->mp_model->insert_draw_logs(array(
                    'game_id'=>$game_id,
                    'role_id'=>$b_role['UserID'],
                    'insert_time'=>time(),
                    'num'=>$num,
                    'type'=>'collect'
                ));
                echo json_encode(array('code'=>1,'msg'=>'领取成功!'));exit;
            }else{
                echo json_encode(array('code'=>0,'msg'=>'领取失败!'));exit;
            }
        }else{
            echo json_encode(array('code'=>0,'msg'=>'未达成,无法领取!'));exit;
        }
    }*/

    // 老玩家召回
    public function get_invite_info() {
        $role_id = $this->input->post('role_id');
        $game_id = $this->input->post('game_id');

        $role = $this->player_model->get_role_info($role_id);
    }

    public function minigame() {
        $game_ids = array(36,54);
        $union_id = $this->input->post('union_id');

        foreach($game_ids as $game_id) {
            $this->player_model->set_database($game_id);

        }

    }

    public function oauth3()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        log_message("error","微信登录接口");
        log_message("error",var_export($_POST,true));

        $code = $this->input->post('code');
        $channel_id = $this->input->post('channel_id');
        $channel = $this->server_model->get_one_channel($channel_id);
        $dismantle_id = $this->input->post('dismantle_id');

        if(!$channel) {
            echo json_encode(array('status' => false, 'msg' => '渠道不存在'));
        }

        $game = $this->server_model->get_game_by_id($channel['game_id']);

        $this->load->library('CI_Wechat', array('game_id' => $channel['game_id']));

        $mp_config = $this->mp_model->get_mp_config($channel['game_id']);

        $this->load->model('user_model');
        $this->load->model('player_model');
        $this->load->model('activity_model');

        $this->player_model->set_database($channel['game_id']);

        // 获取TOKEN
        $token = $this->ci_wechat->getOauthAccessToken($code);
        log_message("error", "获取access token");
        log_message("error", var_export($token, TRUE));

        if (!$token) {
            log_message("error", $this->ci_wechat->errCode);
            log_message("error", $this->ci_wechat->errMsg);
            echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg));
            exit;
        }

        // 首先查询本地邀请记录
//        $user = $this->mp_model->get_mp_user_by_openid($token['openid']);

        // 未注册
//        if (!$user) {
//            $user = $this->ci_wechat->getOauthUserinfo($token['access_token'], $token['openid']);
//            log_message("error", "获取用户信息");
//            log_message("error", var_export($user, TRUE));
//
//            if (!$user) {
//                log_message("error", $this->ci_wechat->errMsg);
//                log_message("error", $this->ci_wechat->errCode);
//                echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg));
//                exit;
//            } else {
//                $user['reg_time'] = time();
//                $user['role_id'] = 0;
//                $user['game_id'] = $channel['game_id'];
//
//                unset($user['privilege']);
//                $this->db->insert('mp_user', $user);
//
//                $user_id = $this->db->insert_id();
//                $this->db->where('user_id',$user_id);
//                $query = $this->db->get('mp_user');
//                $user = $query->row_array();
//            }
//        }

        $dismantle = $this->activity_model->get_dismantle_by_id($dismantle_id);


        if(!$dismantle) {
            echo json_encode(array('status' => false, 'msg' => '未发现该活动'));
            exit(0);
        }

        if($dismantle['status'] == 4) {
            echo json_encode(array('status' => false, 'msg' => '已结束'));
            exit(0);
        }

        $user = $this->ci_wechat->getOauthUserinfo($token['access_token'], $token['openid']);

        $is_myself = false;

        if(isset($user['unionid'])&&!empty($user['unionid'])) {

            // 查询用户信息
            $role = $this->player_model->get_role_info_by_useruin($user['unionid']);

            if (!$role) {
                // 查询记录
                $this->db->where('openid', $token['openid']);
                $this->db->where('dismantle_id', $dismantle_id);
                $this->db->where('from_role_id', $dismantle['role_id']);
                $query = $this->db->get('dismantle_invite');
                $result = $query->row_array();

                if (!$result) {
                    $data = array(
                        'from_role_id' => $dismantle['role_id'],
                        'dismantle_id' => $dismantle_id,
                        'openid' => $token['openid'],
                        'unionid' => $token['unionid'],
                        'create_time' => time(),
                        'game_id' => $channel['game_id']
                    );

                    $this->db->insert('dismantle_invite', $data);
                }
            } else {
                if($role['UserID'] == $dismantle['role_id']) {
                    $is_myself = true;
                }
            }
        }


        echo json_encode(array('status' => true, 'msg' => '','data'=>array('is_myself'=>$is_myself)));exit;
    }

    public function redirect3() {

        $url = $this->input->get('url');
        $channel_id = $this->input->get('channel_id');
        $dismantle_id = $this->input->get('dismantle_id');

        $url .= '?channel_id='.$channel_id.'&dismantle_id='.$dismantle_id;

//        echo $url;exit;

        $channel = $this->server_model->get_one_channel($channel_id);

        $mp_config = $this->mp_model->get_mp_config($channel['game_id']);

        $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$mp_config['appid'].'&redirect_uri='.urlencode($url).'&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect';
//        echo $url;exit;
        Header("Location: $url");
    }

    public function oauth4()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        log_message("error","微信登录接口");
        log_message("error",var_export($_POST,true));

        $code = $this->input->post('code');
        $game_id = $this->input->post('game_id');

        $game = $this->server_model->get_game_by_id($game_id);

        $this->load->library('CI_Wechat', array('game_id' => $game_id));

        $mp_config = $this->mp_model->get_mp_config($game_id);

        $this->load->model('user_model');
        $this->load->model('player_model');
        $this->load->model('activity_model');

        $this->player_model->set_database($game_id);

        // 获取TOKEN
        $token = $this->ci_wechat->getOauthAccessToken($code);
        log_message("error", "获取access token");
        log_message("error", var_export($token, TRUE));

        if (!$token) {
            log_message("error", $this->ci_wechat->errCode);
            log_message("error", $this->ci_wechat->errMsg);
            echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg));
            exit;
        }

        // 首先查询本地邀请记录
        $user = $this->mp_model->get_mp_user_by_openid($token['openid']);

        //未注册
        if (!$user) {

            $user = $this->ci_wechat->getOauthUserinfo($token['access_token'], $token['openid']);

            log_message("error", "获取用户信息");
            log_message("error", var_export($user, TRUE));

            if (!$user) {
                log_message("error", $this->ci_wechat->errMsg);
                log_message("error", $this->ci_wechat->errCode);
                echo json_encode(array('status' => false, 'msg' => $this->ci_wechat->errMsg));
                exit;
            } else {
                $user['reg_time'] = time();
                $user['role_id'] = 0;
                $user['game_id'] = $game_id;

                unset($user['privilege']);
                $this->db->insert('mp_user', $user);

                $user_id = $this->db->insert_id();
                $this->db->where('user_id',$user_id);
                $query = $this->db->get('mp_user');
                $user = $query->row_array();
            }
        }

        // 查询角色
        if ($user['role_id'] > 0) {
            // 已绑定
            $role = $this->player_model->get_role_info($user['role_id']);
        } else {
            // 未绑定
            $role = $this->player_model->get_role_info_by_useruin($user['unionid']);

            if ($role) {
                $this->db->where('openid', $token['openid']);
                $this->db->update('mp_user', array('role_id' => $role['UserID']));
            }
        }

        if (!$role) {
            $role = array(
                'UserID'=>$user['user_id']+1000000000,
                'NickName'=>$user['nickname']
            );
        }

        $link  = $this->server_model->get_game_link($game_id,1);

        if(!$link) {
            echo json_encode(array('status' => false, 'msg' => '链接不存在'));
            exit;
        }

        $params = json_decode($link['params'],TRUE);

        $data = array(
            'user_id'=>$role['UserID'],
            'game_id'=>$game_id,
            'username'=>$role['NickName'],
            'server_id'=>0,
            'time'=>time()
        );

        ksort($data);
        reset($data);

        $str = implode('',$data).$params['key'];

        $sign = md5($str);

        $data['sign'] = $sign;

        $url = $link['url'].'?'.http_build_query($data);

        $log = array(
            'link_id'=>$link['link_id'],
            'role_id'=>$role['UserID'],
            'game_id'=>$game_id,
            'create_time'=>time()
        );

        $this->db->insert('link_logs',$log);

        echo json_encode(array('status' => true, 'msg' => '','data'=>array('url'=>$url)));exit;
    }

    public function redirect4() {

        $url = $this->input->get('url');
        $game_id = $this->input->get('game_id');

        $url .= '?game_id='.$game_id;

        $game = $this->server_model->get_game_by_id($game_id);

        $mp_config = $this->mp_model->get_mp_config($game_id);

        $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$mp_config['appid'].'&redirect_uri='.urlencode($url).'&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect';
//        echo $url;exit;
        Header("Location: $url");
    }

    /**
     * 查询角色
     *
     */
    public function queryRole()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $role_id = $this->input->post('role_id');
        $game_id = $this->input->post('game_id');

        if(!$role_id || !$game_id) {
            echo json_encode(array('status' => false, 'msg' => '参数非法'));exit;
        }

        $this->player_model->set_database($game_id);
        $role = $this->player_model->get_role_info($role_id);

        if(!$role) {
            echo json_encode(array('status' => false, 'msg' => '未查询到角色'));exit;
        }

        $this->load->model('user_model');

        // 查询AGENT
        $agent = $this->user_model->get_one_agent($role_id,$game_id);

        $role['agent'] = $agent;

        if($agent) {
            $role['InsureScore'] = $agent['score'];
        } else {
            $score = $this->player_model->get_role_score($role_id);
            $role['InsureScore'] = $score['RoomCard'];
        }

        echo json_encode(array('status' => true, 'msg' => '','data'=>$role));exit;

    }

}
