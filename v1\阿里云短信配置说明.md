# 阿里云短信服务配置说明

## 概述
已将原有的云之讯短信服务替换为阿里云短信服务。本文档说明如何配置阿里云短信服务。

## 修改的文件
1. `user.php` - 主要的用户接口文件，包含短信发送逻辑
2. `AliyunSms.class.php` - 新增的阿里云短信服务类
3. `sms_config.php` - 短信服务配置文件

## 配置步骤

### 1. 开通阿里云短信服务
1. 登录阿里云控制台
2. 开通短信服务
3. 创建AccessKey（建议使用子账户并授予短信服务权限）

### 2. 申请短信签名和模板
1. 在阿里云短信服务控制台申请短信签名
2. 申请短信模板，模板内容示例：`您的验证码是${code}，请在5分钟内使用。`
3. 等待审核通过

### 3. 修改配置文件
编辑 `sms_config.php` 文件，替换以下配置：

```php
$sms_config = array(
    // 替换为您的AccessKey ID
    'accessKeyId' => 'LTAI5tXXXXXXXXXXXXXX',
    
    // 替换为您的AccessKey Secret
    'accessKeySecret' => 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    
    // 替换为您申请的短信签名
    'signName' => '您的应用名称',
    
    // 替换为您申请的短信模板CODE
    'templateCode' => 'SMS_123456789'
);
```

### 4. 短信模板参数说明
阿里云短信模板中的变量格式为 `${变量名}`，当前代码中使用的变量为：
- `${code}` - 验证码

确保您的短信模板包含此变量。

## 接口兼容性
新的阿里云短信服务类保持了与原有云之讯接口的兼容性：
- `templateSMS($appId, $phone, $templateId, $code)` 方法保持不变
- 返回的JSON格式与原有格式兼容
- 成功响应码仍为 "000000"

## 安全建议
1. 不要将AccessKey信息提交到版本控制系统
2. 建议使用子账户并只授予短信服务权限
3. 定期轮换AccessKey
4. 在生产环境中，可以考虑使用环境变量或加密配置文件

## 测试
配置完成后，可以通过以下方式测试：
1. 调用 `get_verify_code_post` 接口
2. 检查手机是否收到验证码短信
3. 查看返回的响应码是否为 "000000"

## 故障排除
如果短信发送失败，请检查：
1. AccessKey ID和Secret是否正确
2. 短信签名是否已审核通过
3. 短信模板是否已审核通过
4. 手机号码格式是否正确
5. 账户余额是否充足

## 费用说明
阿里云短信服务按条收费，具体费用请参考阿里云官网定价。建议设置费用预警避免超支。
