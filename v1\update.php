<?php
require(APPPATH.'/libraries/REST_Controller.php');

class Update extends REST_Controller
{
	 public function __construct() 
    {
        parent::__construct();
		$this->load->model('server_model');
	}

	/**
	 * @param $version 版本号
	 * @param $channel 渠道号
	 * 
	 * @return code  0:
	code:1,msg:...  显示消息，终止游戏流程
	code:200,... 资源更新
	code:10,版本审核
	code:201, game_url:... 跳转下载更新
	code:0, 正常进入，可以携带额外字段：notice:公告，rollmsg:滚动消息
	 */


	//获取最新版本号
	public function version_get()
	{
		$version_no = $this->input->get('version_no');
        $app_version = $this->input->get('app_version');
        $uuid = $this->input->get('uuid');//唯一标识符

		// 获取游戏配置
		$game = $this->server_model->get_one_game($this->_channel['game_id']);

		// 检查是否未审核状态
		if($this->_channel['is_audit'] == 1) {
			// TODO 判断审核版本号
			if($this->_channel['audit_version'] == substr($version_no,0,8)) {
				$this->response(array('code'=>10,'msg'=>'版本审核中','notice'=>'','rollmsg'=>'','activities'=>0,'host_address'=>'*************','host_port'=>10086),200);
			}
		}

		$ret = array();


		if($game) {
			$ret['is_open_giftbag'] = $game['is_open_giftbag']*1;
		} else {
			$ret['is_open_giftbag'] = 1;
		}

		$arr = explode('.',$version_no);
		$version_no = sprintf('%8d.%03d.%03d',$arr[0],isset($arr[1])?$arr[1]:000,isset($arr[2])?$arr[2]:000);

		// 获取最新的URL更新
		$this->db->where('type',3);
		$this->db->where('channel_id',$this->_channel['channel_id']);
		$this->db->where('version_no >',$version_no);
		$this->db->join('channel_version','channel_version.version_id=version.version_id');
		$this->db->order_by('version_no','desc');
		$query = $this->db->get('version');
		$version = $query->row_array();

		if(!empty($version)) {
			if(($version['is_debug'] == 0) || ($version['is_debug'] == 1)&&($this->server_model->check_version_whitelist($this->getIp())))  {
				$ret['code'] = 201;
				$ret['msg'] = 'URL更新';
				$ret['latest_version'] = $version['version_no'];
				$ret['total_size'] = 0;
				$ret['content'] = $version['content'];
				$ret['game_url'] = $this->_channel['homepage'];
				$this->response($ret,200);
			}
		}

		// 检查资源更新
		$this->db->select("version.version_id,version_no,resource_url,resource_size,resource_md5,content,is_restart,is_debug");
		$this->db->where('type',1);
		$this->db->where('channel_id',$this->_channel['channel_id']);
		$this->db->where('version_no >',$version_no);
		$this->db->join('channel_version','channel_version.version_id=version.version_id');
		$this->db->order_by('version_no','asc');
		$query = $this->db->get('version');
		$versions = $query->result_array();

		$test_versions = array();

		if(!empty($versions)) {
			$is_restart = 0;
			$latest_version_no = '';
			$total_size = 0;

			foreach ($versions as $key => $version) {
				if (!empty($version['resource_url'])) {

					// 安卓更换https->http
					if($this->_channel['platform'] == 1) {
						$versions[$key]['resource_url'] = str_replace('https','http',$version['resource_url']);
					} else {
						$versions[$key]['resource_url'] = $version['resource_url'];
					}
				}
				if (!empty($version['version_no']))
					$versions[$key]['version_no'] = substr($version['version_no'], strpos($version['version_no'], '.') - strlen($version['version_no']) + 1);

				//累积资源包的大小
				if ($version['resource_size']) {
					$total_size += $version['resource_size'];
					$versions[$key]['resource_size'] = (int)$version['resource_size'];
				}

				$latest_version_no = $version['version_no'];

				if ($version['is_restart'] == 1) {
					$is_restart = 1;
				}

				$test_versions = $versions;

				if ($version['is_debug'] == 1) {
					if(!$this->server_model->check_version_whitelist($this->getIp())) {
						unset($versions[$key]);
						$test_versions[] = $version;
					}
				}
			}

			if($this->server_model->check_version_whitelist($this->getIp())) {
				if(!empty($test_versions)) {
					$ret['code'] = 200;
					$ret['msg'] = '资源更新';
					$ret['latest_version'] = $latest_version_no;
					$ret['restart'] = $is_restart;

					if ($total_size > 1024 * 1024) {
						$ret['total_size'] = `` . ' MB';
					} else {
						$ret['total_size'] = floor($total_size / 1024) . ' KB';
					}

					$ret['content'] = '发现新版本(大小：' . $ret['total_size'] . '),是否立即更新？';
					$ret['versions'] = $test_versions;

					$this->response($ret, 200);
				}
			} else {
				if(!empty($versions)) {
					$ret['code'] = 200;
					$ret['msg'] = '资源更新';
					$ret['latest_version'] = $latest_version_no;
					$ret['restart'] = $is_restart;

					if ($total_size > 1024 * 1024) {
						$ret['total_size'] = `` . ' MB';
					} else {
						$ret['total_size'] = floor($total_size / 1024) . ' KB';
					}

					$ret['content'] = '发现新版本(大小：' . $ret['total_size'] . '),是否立即更新？';
					$ret['versions'] = $versions;

					$this->response($ret, 200);
				}
			}

		}

		$ret['code'] = 0;
		$ret['msg'] = "没有更新";

        if (!empty($uuid)){
            //获取uuid
            $id['uuid'] = $uuid;
            //查找后台配置
            $data = $this->server_model->get_one_server_config($id);
            //如果已配置
            if (!empty($data)){
                $ret['host_address'] = $data['host_address'];
                $ret['host_port'] = (int)$data['host_port'];
            }else{
                $data['channel_id'] = $this->_channel['channel_id'];
                $data['version_no'] = substr($version_no,0,8);
                //查找后台配置
                $data = $this->server_model->get_one_server_config($data);
                //如果已配置
                if (!empty($data)){
                    $ret['host_address'] = $data['host_address'];
                    $ret['host_port'] = (int)$data['host_port'];
                }
            }
        }else{
            $data['channel_id'] = $this->_channel['channel_id'];
            $data['version_no'] = substr($version_no,0,8);
            //查找后台配置
            $data = $this->server_model->get_one_server_config($data);
            //如果已配置
            if (!empty($data)){
                $ret['host_address'] = $data['host_address'];
                $ret['host_port'] = (int)$data['host_port'];
            }
        }

/*		if(substr($version_no,0,8) == '20170516') {
			if(in_array($this->_channel['game_id'],array(6))) {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10086;
			}
//			else if(in_array($this->_channel['game_id'],array(14))) {
//				if(in_array($this->_channel['channel_id'],array(10010001,10010003))) {
//					$ret['host_address'] = '*************';
//					$ret['host_port'] = 10086;
//				}
//			}
		}

		if(substr($version_no,0,8) == '20170601') {
			if (in_array($this->_channel['game_id'], array(6))) {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10086;
			}
		}

		if(substr($version_no,0,8) == '20170606') {
			if (in_array($this->_channel['game_id'], array(6))) {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10086;
			}
		}

	 	if(substr($version_no,0,8) == '20170606') {
			if (in_array($this->_channel['channel_id'], array(10010003))) {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10086;
			}
		}

		if(substr($version_no,0,8) == '20170609') {
			if (in_array($this->_channel['game_id'], array(14))) {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10087;
			}
		}

		if(substr($version_no,0,8) == '20170609') {
			if (in_array($this->_channel['game_id'], array(6))) {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10086;
			}
		}

        if(substr($version_no,0,8) == '20170613') {
            if (in_array($this->_channel['game_id'], array(6))) {
                $ret['host_address'] = '*************';
                $ret['host_port'] = 10087;
            }
        }

		if(substr($version_no,0,8) == '20170531') {
			if(in_array($this->_channel['game_id'],array(9,14))) {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10086;
			}
		}

        if(substr($version_no,0,8) == '20170613') {
            if (in_array($this->_channel['game_id'], array(17))) {
                $ret['host_address'] = 'lhmj.tuo3.com.cn';
                $ret['host_port'] = 10097;
            }
        }

        if(substr($version_no,0,8) == '20170622') {
            if(in_array($this->_channel['game_id'],array(9))) {
                $ret['host_address'] = '*************';
                $ret['host_port'] = 10086;
            }
        }

        if(substr($version_no,0,8) == '20170620') {
            if (in_array($this->_channel['game_id'], array(17))) {
                $ret['host_address'] = 'lhmj.tuo3.com.cn';
                $ret['host_port'] = 10097;
            }
        }

		if(substr($version_no,0,8) == '20170527') {
			if(in_array($this->_channel['game_id'],array(15))) {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10086;
			}
		}

		if(substr($version_no,0,8) == '20179999') {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10086;
		}

//		if(substr($version_no,0,8) == '20170601') {
//			if(in_array($this->_channel['game_id'],array(15))) {
//				$ret['host_address'] = '*************';
//				$ret['host_port'] = 10086;
//			}
//		}

		if(substr($version_no,0,8) == '20170519') {
			if(in_array($this->_channel['game_id'],array(14,15))) {
				$ret['host_address'] = '*************';
				$ret['host_port'] = 10086;
			}
		}*/

		// 获取公告
		$notice = $this->server_model->get_latest_notice($this->_channel['channel_id'],1);
		$ret['notice'] = $notice?$notice['content']:'';

		$rollmsg = $this->server_model->get_latest_notice($this->_channel['channel_id'],2);
		// 获取滚动消息
		$ret['rollmsg'] = $rollmsg?$rollmsg['content']:'';
		// 获取活动
		$ret['activities'] = 0;


		$this->response($ret,200);
	}

    //添加更新服务器配置
    public function insert_uuid_get()
    {
        $config['uuid'] = $this->input->get('uuid');
        $list_id = $this->input->get('list_id');

        if (!empty($config['uuid'])){
            $res = $this->server_model->get_uuid_data($config['uuid']);
            $data['uuid'] = $config['uuid'];
            $data['list_id'] = $list_id;
            if ($res){
                $this->server_model->update_server_config($res['config_id'],$data);
                $ret['msg'] = '更新成功';
                $this->response($ret,200);
            }else{
                $this->server_model->insert_server_config($data);
                $ret['msg'] = '添加成功';
                $this->response($ret,200);
            }
        }else{
            $ret['msg'] = '添加失败,uuid不能为空';
            $this->response($ret,200);
        }
    }

    //获取服务器列表
    public function get_server_get()
    {
        $ret['data'] = $this->server_model->get_server_list();
        $this->response($ret,200);
    }

    //添加服务器配置
    public function delete_server_uuid_get()
    {
        $uuid = $this->input->get('uuid');
        if (!empty($uuid)){
            if ($this->server_model->delete_uuid_data($uuid)){
                $ret['msg'] = '删除成功';
            }else{
                $ret['msg'] = '删除失败';
            }
        }else{
            $ret['msg'] = 'uuid请勿为空';
        }
        $this->response($ret,200);
    }

	//获取用户IP地址
	public function getIp()
	{

		if(!empty($_SERVER["HTTP_CLIENT_IP"]))
		{
			$cip = $_SERVER["HTTP_CLIENT_IP"];
		}
		else if(!empty($_SERVER["HTTP_X_FORWARDED_FOR"]))
		{
			$cip = $_SERVER["HTTP_X_FORWARDED_FOR"];
		}
		else if(!empty($_SERVER["REMOTE_ADDR"]))
		{
			$cip = $_SERVER["REMOTE_ADDR"];
		}
		else
		{
			$cip = '';
		}
		preg_match("/[\d\.]{7,15}/", $cip, $cips);
		$cip = isset($cips[0]) ? $cips[0] : 'unknown';
		unset($cips);

		return $cip;
	}
	
}