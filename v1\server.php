<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Server extends Auth_Controller {
	/**
	 * 主控制器
	 * <AUTHOR> 
	 * @date 2014-3-13
	 * @copyright Tuo3.Inc
	*/
	public function __construct() 
	{
        parent::__construct();
        $this->load->model('server_model','',TRUE);
        $this->load->model('pg_model');

    }

    /**
     * [首页]
     */
	public function index()
	{
		self::channels();
	}

	/**
	 * [渠道列表](2014-03-19)
	 */
	public function channels()
	{
		$this->_data['page_title'] = '渠道管理';

		$this->_data['partners'] = unserialize($this->session->userdata('partners'));
		$this->_data['games'] = unserialize($this->session->userdata('games'));
		
		//取得用户下权限的所有渠道
		$this->_data['channels'] = $this->server_model->get_all_channels();

		//取得平台配置
		$this->_data['platform'] = $this->config->item('platform');


		$this->load->view('server/channel_lists',$this->_data);
	}


	/**
	 * [更新渠道]
	*/
	public function channel_update()
	{
		//解析键值
		$data = array($_POST['name']=>$_POST['value']);

		$where = array('channel_id'=>$_POST['pk']);

		if($this->server_model->update_channel($data,$where))
		{
			show_response(0,'更新成功');
		}

		show_response(1,'更新失败'); 
	}

	/**
	 * [渠道配置页面]
	*/
	public function channel_setting()
	{
		$channel_id = $this->uri->segment(3);

		//获取某个channel
		$this->_data['channel'] = $this->server_model->get_one_channel($channel_id);
	
		$this->load->view('server/channel_setting',$this->_data);
	}

	/**
	 * [渠道配置更新页面]
	*/
	public function channel_setting_update()
	{
		$data = $this->inpinputut->post();

		if(isset($data['channel_id'])&&!empty($data['channel_id'])) {
			$data['is_audit'] = isset($data['is_audit'])&&$data['is_audit']=='on'?1:0;

			$this->db->update('channel',$data,array('channel_id'=>$data['channel_id']));
			if($this->db->affected_rows()>0) {
				show_response(0,'更新成功');
			} else {
				show_response(1,'更新失败');
			}		
		}
	}


	/**
	 * [添加渠道]
	 */
	public function channel_add()
	{

		// 父渠道
		$parent_id = $this->input->post('parent');
		$channel_id = $this->input->post('id');
		$channel_key = $this->input->post('key');

		//渠道名称
		$channel_name = trim( $this->input->post('name'));
		//对应平台
		$platform = $this->input->post('platform');

		$game = unserialize($this->session->userdata('game'));
		$game_id = $game['game_id'];

		//返回数组
		$ret = array();

			$data = array(
				'channel_id'=>$channel_id,
				'game_id'=>$game_id,
				'channel_name'=>$channel_name,
				'appkey'=>$channel_key,
				'platform'=>$platform,
				'create_time'=>time());

			if($this->server_model->insert_channel($data))
			{
				$ret['code'] = 0;
				$ret['msg'] = '添加成功';
				echo json_encode($ret);exit;
			} else 
			{
				$ret['code'] = 1;
				$ret['msg'] = '添加失败';
				echo json_encode($ret);exit;
			}
	}

	//删除渠道
	public function channel_delete()
	{
		$id = isset($_POST['id'])?$_POST['id']:'';
		$ret = array();
		$ret['code'] = 1;

		if(!empty($id))
		{
			if($this->server_model->delete_channel($id))
			{
				$ret['code'] = 0;
			} else {
				$ret['msg'] = '删除失败';	
			}			
		} else {
			$ret['msg'] = '参数为空';
		}

		echo json_encode($ret);
	}

	// 动态获取渠道
	public function ajax_channels()
	{
		$partners = $this->input->post('partner');
		$games = $this->input->post('game');

		$where = '1=1';

		if($partners)
		{
			$where .= ' and (';
			foreach(explode(',',$partners) as $key=>$partner)
			{
				if($partner)
				{
					if($key>0)
					{
						$where .= ' or `tuo3_channel`.`partner_id`='.$partner;	
					} else {
						$where .= ' `tuo3_channel`.`partner_id`='.$partner;
					}					
				}
				
			}
			$where .= ' )';
		} 
			
		if($games)
		{
			$where .= ' and (';
			foreach(explode(',',$games) as $key=>$game)
			{
				if($game)
				{
					if($key>0)
					{
						$where .= ' or `tuo3_channel`.`game_id`='.$game;
					} else {
						$where .= ' `tuo3_channel`.`game_id`='.$game;
					}	
				
				}				
			}
			$where .= ' )';
		}	
		
		$channels = $this->server_model->get_all_channels($where);

		echo json_encode($channels);
	}

	// 根据platform 动态获取对应的渠道和区服
	public function ajax_channel_zone()
	{
		$platform = $this->input->post('platform');

		if($platform) {
			$channels = $this->server_model->get_channel_by_platform($platform);
			$zones = $this->server_model->get_all_zones(array('platform'=>$platform));
		} else {
			$channels = $this->server_model->get_channel_by_platform();
			$zones = $this->server_model->get_all_zones();
		}
		
		$channel_options = array();

		foreach ($channels as $key => $channel) {
			$option = array();
			$option['label'] = $channel['channel_name'];
			$option['title'] = $channel['channel_name'];
			$option['value'] = $channel['channel_id'];
			$channel_options[] = $option;
		}

		$zone_options = array();

		foreach ($zones as $key => $zone) {
			$option = array();
			$option['label'] = $zone['zone_name'];
			$option['title'] = $zone['zone_name'];
			$option['value'] = $zone['zone_id'];
			$zone_options[] = $option;
		}

		echo json_encode(array('channels'=>$channel_options,'zones'=>$zone_options));exit;
	}

	/**
	 * 游戏大区列表(2014-03-18) 暂不使用
	 */
	// public function bigzone_lists()
	// {
	// 	$this->_data['page_title'] = '游戏大区列表';
	// 	$this->load->view('server/bigzone_lists',$this->_data);
	// }

	/**
	 * [bigzone_add 添加大区] 暂不使用
	 */
	// public function bigzone_add()
	// {
	// 	$name = isset($_POST['name'])?$_POST['name']:'';
	// 	$cid = isset($_POST['channel'])?$_POST['channel']:'';

	// 	if(!empty($name) && !empty($cid))
	// 	{
	// 		$data = array('bigzone_name'=>$name,
	// 						'channel_id'=>$cid,
	// 						'create_time'=>time());
	// 		$bigzone_id = $this->server_model->insert_bigzone($data);

	// 		if($bigzone_id)
	// 		{
	// 			$ret['code'] = 0;
	// 			$ret['value'] = $bigzone_id;
	// 		} else 
	// 		{
	// 			$ret['code'] = 1;
	// 			$ret['msg'] = '插入失败';
	// 		}
	// 	} else {
	// 		$ret['code'] = 1;
	// 		$ret['msg'] = '参数为空';
	// 	}
	// 	echo json_encode($ret);exit;
	// }

	/**
	 * [bigzone_update 大区更新] 暂不使用
	 */
	// public function bigzone_update()
	// {
	// 	$data = array($_POST['name']=>$_POST['value']);
	// 	$where = array('bigzone_id'=>$_POST['pk']);
	// 	if(!$this->server_model->update_bigzone($data,$where))
	// 	{
	// 		$ret['code'] = 1;
	// 		$ret['msg'] = '更新失败';
	// 		echo json_encode($ret);exit;
	// 	} else {
	// 		$ret['code'] = 0;
	// 		$ret['value'] = $_POST['value'];
	// 		echo json_encode($ret);exit;
	// 	}
	// }

	/**
	 * 游戏服列表(2014-03-18)
	 */
	public function zones()
	{
		$this->_data['page_title'] = '游戏服列表';

		$platform = $this->input->get('platform');

		$where = array();

		if($platform) {
			$where['platform'] = $platform;
		}

		$this->_data['partners'] = unserialize($this->session->userdata('partners'));
		$this->_data['games'] = unserialize($this->session->userdata('games'));

		$this->_data['channels'] = $this->server_model->get_all_channels();
				
		$this->_data['zones'] = $this->server_model->get_all_zones($where);
		// echo $this->db->last_query();
		// var_dump($this->_data['zones']);

		$this->_data['status'] = $this->config->item('zone_status');
		$this->_data['type'] = $this->config->item('zone_type');

		$this->load->view('server/zone_lists',$this->_data);
	}

	/**
	 * [zone_add 添加区服]
	 * @return [json] [添加结果]
	 */
	public function zone_add()
	{
		// parse_str($_POST['zone'], $zoneArr);
		$partner_id = (int)$this->input->post('partner');
		$game_id = (int)$this->input->post('game');
		$bigzone = $this->input->post('bigzone');
		$name  = trim($this->input->post('name'));
		$open_time = $this->input->post('open_time').'.00';
		$ip = $this->input->post('ip');	
		$db_ip = $this->input->post('db_ip');	
		$db_log_ip = $this->input->post('db_log_ip');	
		$port = $this->input->post('port');	
		$db_name = $this->input->post('db_name');	
		$status = $this->input->post('status');	
		$type = $this->input->post('type');	
		$platform = $this->input->post('platform');	
	
		//验证参数是否合法
		if(empty($partner_id)||empty($game_id)||empty($name)||empty($ip))
		{
			show_response(1,'参数非法');
		}

		$data = array('partner_id'=>$partner_id,
					'game_id'=>$game_id,
					'bigzone_id'=>$bigzone,
					'zone_name'=>$name,
					'open_time'=>$open_time,
					'ip'=>$ip,
					'db_ip'=>$db_ip,
					'db_log_ip'=>$db_log_ip,
					'db_name'=>$db_name,
					'port'=>$port,
					'status'=>$status,
					'type'=>$type,
					'create_time'=>time());

		$zone_id = $this->server_model->insert_zone($data);

		if($zone_id)
		{
			// 更新smallzone
			$this->server_model->update_zone(array('smallzone_id'=>$zone_id),array('zone_id'=>$zone_id));
			show_response(0,'添加成功');
		} 
		show_response(1,'添加失败');
	}

	/**
	 * [zone_update 区服更新]
	 * @return [json] [更新结果]
	 */
	public function zone_update()
	{
		$data = array($_POST['name']=>$_POST['value']);
		$where = array('zone_id'=>$_POST['pk']);
		if(!$this->server_model->update_zone($data,$where))
		{
			$ret['code'] = 1;
			$ret['msg'] = '更新失败';
		} else {
			$ret['code'] = 0;
			$ret['value'] = $_POST['value'];
		}
		echo json_encode($ret);exit;

	}

	public function zone_audit()
	{
		$zone_id = $this->input->post('zone');
		$status = $this->input->post('status');
		$version = $this->input->post('version');

		if(empty($zone_id)) {
			show_response(1,'缺少参数zone');
		}

		if(!isset($status)) {
			show_response(1,'缺少参数status');
		}

		if($status == 1 && empty($version)) {
			show_response(1,'缺少参数version');
		}

		$data = array('is_audit'=>$status,'audit_version'=>$version);
		$where = array('zone_id'=>$zone_id);
		if($this->server_model->update_zone($data,$where))
		{
			show_response(0,'操作成功');
		} else {
			show_response(1,'操作失败');
		}

	}

	public function zone_delete()
	{
		$id = isset($_POST['id'])?$_POST['id']:'';
		$ret = array();
		$ret['code'] = 1;

		if(!empty($id))
		{
			if($this->server_model->delete_zone($id))
			{
				$ret['code'] = 0;
			} else {
				$ret['msg'] = '删除失败';	
			}			
		} else {
			$ret['msg'] = '参数为空';
		}

		echo json_encode($ret);
	}

	/**
	 * 区服操作
	 * @return [type] [description]
	 */
	public function zone_operate()
	{
		$zone_ids = $this->input->post('zone');
		$type = $this->input->post('type');
		$action = $this->input->post('action');
		$remark =  $this->input->post('remark');

		if(!empty($zone_ids)) {
			if($type == 1) { // 快速执行
				$this->db->where_in('zone_id',$zone_ids);
				if($action == 2 && empty($remark)) {
					$remark = '服务器维护中，请稍后再试！';
				}
				$this->db->update('zone',array('remark'=>$remark,'status'=>$action));
				show_response(0,"操作成功");
			}		
		} else {
			show_response(1,'参数非法');
		}
		
	}

	/**********************************公告管理开始***********************************/

	/**
	 * [notices 公告管理]
	 * @return [type] [description]
	 */
	public function notices()
	{
		$this->_data['page_title'] = '公告管理';

		//读取公告类型
		$this->_data['type']  = $this->config->item('notice_type');

		//取得所有服务器
		$this->_data['android_zones']= $this->server_model->get_all_zones(array('platform'=>1));
		$this->_data['jail_zones']= $this->server_model->get_all_zones(array('platform'=>2));
		$this->_data['appstore_zones']= $this->server_model->get_all_zones(array('platform'=>3));

		

		$keyword = $this->input->post('keyword');
		$begin_time = $this->input->post('begin');
		$end_time = $this->input->post('end');
		$zones = $this->input->post('zones');
		$type = $this->input->post('type');

		//取得所有公告	
		$this->_data['notices'] = $this->server_model->get_all_notices($zones,$type,$begin_time,$end_time,trim($keyword));
		$this->load->view('server/notice_lists',$this->_data);
	}

	//添加公告 ajax
	public function notice_add()
	{
		//获取参数
		$title = trim($this->input->post('title'));
		$b_title = trim($this->input->post('b_title'));
		$content = trim($this->input->post('content'));
		$begin_time = trim($this->input->post('begin_time'));
		$end_time = trim($this->input->post('end_time'));
		$crycle = (int)$this->input->post('crycle');
		$type = (int)$this->input->post('type');
		$order = (int)$this->input->post('order');
		$status = (int)$this->input->post('status');
		$zones = $this->input->post('zone');

		if(!empty($zones) && !empty($title) && !empty($b_title) &&!empty($content))
		{
			$data = array(
				'b_title'=>$b_title,
				'title'=>$title,
				'content'=>$content,
				'begin_time'=>strtotime($begin_time),
				'end_time'=>strtotime($end_time),
				'type'=>$type,
				'crycle'=>$crycle,
				'status'=>$status,
				'order'=>$order,
				'create_time'=>time(),
				'update_time'=>time());

			// 插入公告	
			$notice_id = $this->server_model->insert_notice($data);			

			if(!empty($notice_id))
			{
				// 插入对应关系
				foreach ($zones as $v) 
				{
					$this->db->insert('zone_notice',array('zone_id'=>$v,'notice_id'=>$notice_id));
				}

				show_response(0,'添加成功');

			} else {

				show_response(1,'添加失败');

			}
		} else {

			show_response(1,'参数非法');
			
		}

	}

	// 公告信息查询
	public function notice_query()
	{
		$keyword = $this->input->get('keyword');
		$begin_time = $this->input->get('begin');
		$end_time = $this->input->get('end');
		$zones = $this->input->get('zones');

		$notices = $this->server_model->get_all_notices();

		echo json_encode($notices);

	}

	//公告编辑
	public function notice_edit()
	{
		// 读取公告类型
		$this->_data['type']  = $this->config->item('notice_type');

		// 取得所有服务器
		$this->_data['zones'] = $this->server_model->get_all_zones();

		// 获取URI
		$notice_id = $this->uri->segment(3);

		// 获取单个notice
		$this->_data['notice'] = $this->server_model->get_notice_by_id($notice_id);

		// 获取notice对应的服务器值
		$query = $this->db->get_where('zone_notice',array('notice_id'=>$notice_id));
		
		$zones = $query->result_array();

		$ids = array();

		if(!empty($zones)) {
			foreach ($zones as $k => $v) {
				$ids[] = $v['zone_id'];
			}
		}

		$this->_data['ids'] = $ids;

		$this->load->view('server/notice_edit',$this->_data);
	}

	//公告信息更新ajax
	public function notice_update()
	{
		//获取参数
		$notice_id = $this->input->post('notice');
		$title = trim($this->input->post('title'));
		$b_title = trim($this->input->post('b_title'));
		$content = trim($this->input->post('content'));
		$begin_time = trim($this->input->post('begin_time'));
		$end_time = trim($this->input->post('end_time'));
		$crycle = (int)$this->input->post('crycle');
		$type = (int)$this->input->post('type');
		$order =  (int)$this->input->post('order');
		//$status = (int)$this->input->post('status');
		$zones = $this->input->post('zone');

		if(!empty($notice_id)&&!empty($zones) && !empty($title) && !empty($b_title) &&!empty($content))
		{
			$data = array(
				'b_title'=>$b_title,
				'title'=>$title,
				'content'=>$content,
				'begin_time'=>strtotime($begin_time),
				'end_time'=>strtotime($end_time),
				'type'=>$type,
				'order'=>$order,
				'status'=>0,
				'update_time'=>time());
			if($this->server_model->update_notice($notice_id,$data)) {

				//$this->db->update('zone_notice',array('status'=>3),array('notice_id'=>$notice_id));
				//$this->db->delete('zone_notice',array('notice_id'=>$notice_id));
				
				foreach ($zones as $v) 
				{
					$query = $this->db->get_where('zone_notice',array('zone_id'=>$v,'notice_id'=>$notice_id));
					if($query->num_rows()>0) {
						$this->db->update('zone_notice',array('status'=>0),array('notice_id'=>$notice_id));
					} else {
						$this->db->insert('zone_notice',array('zone_id'=>$v,'notice_id'=>$notice_id));
					}
				}

				show_response(0,'更改成功');
			} else {
				show_response(1,'更改失败');
			}
		} else {
			show_response(1,'参数非法');
		}
	}

	// 公告删除
	public function notice_delete()
	{
		$id = isset($_POST['id'])?$_POST['id']:'';
		$ret = array();
		$ret['code'] = 1;

		if(!empty($id))
		{
			if($this->server_model->delete_notice($id))
			{
				$ret['code'] = 0;
			} else {
				$ret['msg'] = '删除失败';	
			}			
		} else {
			$ret['msg'] = '参数为空';
		}

		echo json_encode($ret);
	}


	/**********************************公告管理结束***********************************/

	// 活动参数管理
	public function params()
	{
		$this->_data['page_title'] = '活动类型';

		$promotion_id = $this->input->get('promotion_id');

		// 取得所有活动参数
		$this->_data['params'] = $this->server_model->get_all_params($promotion_id);

		// var_dump($this->_data['params'] );

		$query = $this->db->get('item');
		$items = $query->result_array();

		$new_items = array();

		foreach ($items as $k=>$v) {
			$new_items[$v['item_id']]['item_name'] = $v['item_name'];
			$new_items[$v['item_id']]['item_quality'] = $v['item_quality'];
		}

		$this->_data['items'] = $new_items;

		$this->load->view('server/param_lists',$this->_data);
	}



	public function param_add()
	{
		//获取参数
		$promotion_id = trim($this->input->post('promotion'));
		$value1 = trim($this->input->post('value1'));
		$value2 = trim($this->input->post('value2'));
		$percent = trim($this->input->post('percent'));
		$param = trim($this->input->post('param'));
//		$reward_text = trim($this->input->post('reward_text'));
		// $name = $this->input->post('name');
		$desc = $this->input->post('desc');

		$item_ids = $this->input->post('item_id');
		$item_nums = $this->input->post('item_num');
		$item_rates = $this->input->post('item_rate');
		$item_shows = $this->input->post('item_show');

		$reward_text = '';

		foreach($item_ids as $k=>$v) {
			if($v&&is_numeric($item_nums[$k])&&is_numeric($item_rates[$k])&&is_numeric($item_shows[$k])) {
				$reward_text .= $v.','.$item_nums[$k].','.$item_rates[$k].','.$item_shows[$k].',';
			}
		}

		$reward_text = $reward_text?substr($reward_text,0,-1):'';

//		var_dump($reward_text);

		if($promotion_id&&$param)
		{
			$promotion = $this->server_model->get_promotion_by_id($promotion_id);

			if(empty($promotion)) {
				show_response(1,'模版不存在');
			}

			$data = array(
				'promotion_id'=>$promotion_id,
				'k'=>$promotion['k'],
				'v'=>$promotion['v'],
				'param'=>$param,
				'reward_text'=>$reward_text,
				'value1'=>$value1,
				'value2'=>$value2,
				'reward_percent'=>$percent,
				'reward_desc'=>$desc,
				'create_time'=>time(),
				'update_time'=>time());

			// 插入公告	
			$param_id = $this->server_model->insert_param($data);

			if(!empty($param_id)) {
				show_response(0,'添加成功');
			} else {
				show_response(1,'添加失败');
			}	
		} else {
			show_response(1,'参数非法');
		}	
	}

	public function param_edit() 
	{
		$this->_data['page_title'] = '编辑参数';

		$param_id = $this->uri->segment(3);

		$this->_data['param'] = $this->server_model->get_param_by_id($param_id);

		$query = $this->db->get('item');
		$items = $query->result_array();

		$new_items = array();

		foreach ($items as $k=>$v) {
			$new_items[$v['item_id']]['item_name'] = $v['item_name'];
			$new_items[$v['item_id']]['item_quality'] = $v['item_quality'];
		}

		$this->_data['items'] = $new_items;

		$this->load->view('server/param_edit',$this->_data);
	}

	public function param_update()
	{
		//获取参数
		$id = trim($this->input->post('id'));

		$promotion_id = trim($this->input->post('promotion'));
		$value1 = trim($this->input->post('value1'));
		$value2 = trim($this->input->post('value2'));
		$percent = trim($this->input->post('percent'));
		$param = trim($this->input->post('param'));
//		$reward_text = trim($this->input->post('reward_text'));
		// $name = $this->input->post('name');
		$desc = $this->input->post('desc');

		$item_ids = $this->input->post('item_id');
		$item_nums = $this->input->post('item_num');
		$item_rates = $this->input->post('item_rate');
		$item_shows = $this->input->post('item_show');

		$reward_text = '';

		foreach($item_ids as $k=>$v) {
			if($v&&is_numeric($item_nums[$k])&&is_numeric($item_rates[$k])&&is_numeric($item_shows[$k])) {
				$reward_text .= $v.','.$item_nums[$k].','.$item_rates[$k].','.$item_shows[$k].',';
			}
		}

		$reward_text = $reward_text?substr($reward_text,0,-1):'';

		if($id&&$promotion_id&&$param!='')
		{
			$data = array(
				'promotion_id'=>$promotion_id,
				'param'=>$param,
				'reward_text'=>$reward_text,
				'value1'=>$value1,
				'value2'=>$value2,
				'reward_percent'=>$percent,
				'reward_desc'=>$desc,
				'create_time'=>time(),
				'update_time'=>time());

			// 更新参数
			if ($this->server_model->update_param($data,array('id'=>$id))) {
				show_response(0,'更新成功');
			} else {
				show_response(1,'更新失败');
			}	
		} else {
			show_response(1,'参数非法');
		}	
	}

	// 删除
	public function param_delete()
	{
		$id = $this->input->post('id');

		if(!empty($id)) {
			if($this->server_model->delete_param($id)) {
				show_response(0,'删除成功');
			} else {
				show_response(1,'删除失败');
			}
		} else {
			show_response(1,'参数非法');
		}
	}

	public function promotions()
	{
		$this->_data['page_title'] = '活动类型';

		$this->_data['promotions'] = $this->server_model->get_all_promotions();

		$this->load->view('server/promotion_lists',$this->_data);
	}

	public function promotion_add()
	{
		//获取参数
		$k = trim($this->input->post('k'));
		$v = trim($this->input->post('v'));
		$btn_name = trim($this->input->post('btn_name'));
		$reward_repeat = trim($this->input->post('reward_repeat'));
		$name = $this->input->post('name');
		$type = $this->input->post('type');
		$desc = $this->input->post('desc');
		$p1 = $this->input->post('p1');
		$p2 = $this->input->post('p2');

		if($k&&$v)
		{
			$data = array(
				'k'=>$k,
				'v'=>$v,
				'btn_name'=>$btn_name,
				'reward_repeat'=>$reward_repeat,
				'p1'=>$p1,
				'p2'=>$p2,
				'type'=>$type,
				'name'=>$name,
				'desc'=>$desc,
				'create_time'=>time(),
				'update_time'=>time());

			// 插入公告	
			$promotion_id = $this->server_model->insert_promotion($data);

			if(!empty($promotion_id)) {
				show_response(0,'添加成功');
			} else {
				show_response(1,'添加失败');
			}	
		} else {
			show_response(1,'参数非法');
		}	
	}

	public function promotion_update()
	{
		///获取参数
		$id = trim($this->input->post('id'));
		$k = trim($this->input->post('k'));
		$v = trim($this->input->post('v'));
		$btn_name = trim($this->input->post('btn_name'));
		$reward_repeat = trim($this->input->post('reward_repeat'));
		$name = $this->input->post('name');
		$desc = $this->input->post('desc');
		$p1 = $this->input->post('p1');
		$p2 = $this->input->post('p2');
		$type = $this->input->post('type');


		if($id)
		{
			$data = array(
				'k'=>$k,
				'v'=>$v,
				'btn_name'=>$btn_name,
				'reward_repeat'=>$reward_repeat,
				'name'=>$name,
				'desc'=>$desc,
				'p1'=>$p1,
				'p2'=>$p2,
				'type'=>$type,
				'create_time'=>time(),
				'update_time'=>time());

			// 插入公告	
			if ($this->server_model->update_promotion($data,array('id'=>$id))) {

				show_response(0,'更新成功');
			} else {
				show_response(1,'更新失败');
			}	
		} else {
			show_response(1,'参数非法');
		}	

	}

	public function promotion_edit() 
	{
		$this->_data['page_title'] = '编辑模版';

		$param_id = $this->uri->segment(3);

		$this->_data['promotion'] = $this->server_model->get_promotion_by_id($param_id);

		$this->load->view('server/promotion_edit',$this->_data);
	}

	// 删除
	public function promotion_delete()
	{
		$id = $this->input->post('id');

		if(!empty($id)) {
			if($this->server_model->delete_promotion($id)) {
				// 取出所有的参数删除
				$params = $this->server_model->get_all_params($id);

				foreach ($params as $key => $param) {
					$this->server_model->delete_param($param['id']);
				}

				show_response(0,'删除成功');
			} 
		} 
		show_response(1,'参数非法');
		
	}



	public function activities()
	{
		$this->_data['page_title'] = '活动管理';

		// 取得所有活动参数
		$this->_data['promotions'] = $this->server_model->get_all_promotions();

		// $keys = array();

		// foreach ($params as $k => $v) {
		// 	$keys[$v['k']] = $v['name'];
		// }

		// $this->_data['keys'] = $keys;
		// 取得所有服务器
		$this->_data['zones'] = $this->server_model->get_all_zones();

		$this->load->view('server/activity_lists',$this->_data);
	}

	// 添加活动
	public function activity_add()
	{
		$this->load->model('pg_model');

		// 获取区服列表
		$zone_ids = $this->input->post('zone');
		// 获取活动列表
		// $param_ids = $this->input->post('param');
		$param_k = $this->input->post('param_k');
		$param_v= $this->input->post('param_v');

		// 开始时间
		$begin_time = $this->input->post('begin_time');
		// 结束时间
		$end_time = $this->input->post('end_time');
		// 显示开始时间
		$show_begin_time = $this->input->post('show_begin_time');
		// 显示结束时间
		$show_end_time = $this->input->post('show_end_time');		
		// 时间范围1
		$hour_range1 = $this->input->post('hour_range1');
		// 时间范围2
		$hour_range2 = $this->input->post('hour_range2');
		// 显示排序
		$order_id = $this->input->post('order');

		if(is_array($zone_ids)&&count($zone_ids)>0) {
			
			foreach ($zone_ids as $zone_id) {

				// var_dump($zone_id);

				$this->pg = $this->pg_model->connect($zone_id);

	            if(!$this->pg)
	            {
	            	show_response(1,$zone_id.'服务器连接失败,请检查设置后重新添加！');
	            }

	 //            //$dbconn = pg_connect("host=*************** dbname=gunner20004 user=postgres password=123456 port=5432")
  // or die('Could not connect: ' . pg_last_error());

	            //foreach ($param_ids as $param_id) {

	            	// 取出对应的信息
	            	//$param = $this->server_model->get_param_by_id($param_id);


	            	if(!empty($param_k)&&!empty($param_v)) {

	            		// 先删除
		            	$sql = sprintf("DELETE FROM game_param WHERE k='%s'",$param_k);
		            	
		            	$this->pg->query($sql);

		            	//echo $this->pg->last_query();

		            	// 再插入
		            	$data = array('k'=>$param_k,
		            		'v'=>intval($param_v),
		            		'order_id'=>intval($order_id),
		            		'begin_time'=>strtotime($begin_time),
		            		'end_time'=>strtotime($end_time),
		            		'show_time'=>strtotime($show_begin_time),
		            		'show_end_time'=>strtotime($show_end_time),
		            		'hour_range_1'=>trim($hour_range1),
		            		'hour_range_2'=>trim($hour_range2));

		            	$this->pg->insert('game_param',$data);
		            	$sql2 = sprintf("UPDATE game_param SET v=2 WHERE k='%s'",'GAME_PARAM_DB_STATUS');
                        $this->pg->query($sql2);		
		            	//echo $this->pg->last_query();
	            	}
	            	
	            //}
			}
			show_response(0,'设置成功');

		} else {
			show_response(1,'参数非法');
		}

	}

	// 活动删除
	public function activity_delete()
	{
		$zone_id = $this->input->post('zone');
		$k = preg_replace("/\([\s\S]*\)/i","",$this->input->post('k'));
		$v = $this->input->post('v');

		if($zone_id&&$k&&$v!='') {

			$this->pg = $this->pg_model->connect($zone_id);

			if($this->pg) {
				// 删除
		        $sql = sprintf("DELETE FROM game_param WHERE k='%s' and v='%s'",$k,$v);
		            	
		        $query = $this->pg->query($sql);

		        // echo $this->pg->last_query();

		        // $sql = sprintf("SELECT FROM game_param_%s WHERE k='%s' and v='%s'",$zone_id,$k,$v);
		            	
		        // $ret = $this->pg->query($sql);

		        show_response(0,'删除成功');

			}
			show_response(1,'服务器连接失败');

		}

		show_response(1,'参数非法');

	}

	// 活动编辑
	public function activity_edit()
	{
		//获取单个activity
		$this->_data['activity'] = $this->server_model->get_activity_by_id($notice_id);

		$this->load->view('server/activity_edit',$this->_data);
	}


	// 查询活动
	public function activity_query()
	{
		$zone_id = $this->input->post('zone');
		$begin_time = $this->input->post('from');
		$end_time = $this->input->post('to');
		$k = $this->input->post('k');
		$type = $this->input->post('type');

		$promotions = $this->server_model->get_all_promotions();

		$keys = array();

		foreach ($promotions as $v) {
			$keys[$v['k']] = $v['name'];
		}

		if(!empty($zone_id)) {
			$this->load->model('pg_model');
			$this->pg = $this->pg_model->connect($zone_id);

			if(!$this->pg) {
                show_response(1,'连接服务器失败');
            }

            if(!empty($k)) {
            	$this->pg->like('k',trim($k));
            }

            if(!empty($type)) {
            	if($type==1) {
            		$this->pg->not_like('k','ITEM','after');
            	} else if($type==2) {
            		$this->pg->like('k','ITEM','after');
            	}
            }

            if(!empty($begin_time)&&!empty($end_time)) {
            	$this->pg->where('begin_time <',strtotime($begin_time));
            	$this->pg->where('end_time >',strtotime($end_time));
            }

            $query = $this->pg->get('game_param');
            // echo $this->pg->last_query();

            $data = $query->result_array();

            // 处理显示
            foreach ($data as $kk=>$vv) {
            	foreach ($promotions as $v) {
            		if($v['k']==$vv['k']&&$v['v']==$vv['v']) {
            			$data[$kk]['k'] = $vv['k'].'('.$v['name'].')';
            		}
				}

            	// if(isset($keys[$vv['k']])&&!empty($keys[$vv['k']])) {
            	// 	$data[$kk]['k'] = $vv['k'].'('.$keys[$vv['k']].')';
            	// }

				
            }

            echo json_encode(array('code'=>0,'data'=>$data));

		} else {
			show_response(1,'区服不得为空');
		}

	}

	// 更新activity
	public function activity_update()
	{
		$name = $_POST['name'];
		$value = $_POST['value'];
		// 转换为时间戳
		if($name=='show_time'||$name=='show_end_time'||$name=='begin_time'||$name=='end_time') {
			$value = strtotime($value);
		}
		$data = array($name=>$value);
		$where = array('k'=>preg_replace("/\([\s\S]*\)/i","",$_POST['k']),'v'=>$_POST['v']);

		$zone_id = $_POST['zone'];

		// 判断修改
		if(substr($_POST['k'],0,5)==='ITEM_'){
			$v_max = intval($_POST['v_max']);
			if($value>$v_max) {
				show_response(1,'当前值不得超过最大值！');
			}
		}

		if(!empty($zone_id)) {

			$this->load->model('pg_model');
			$this->pg = $this->pg_model->connect($zone_id);

			if(!$this->pg) {
	            show_response(1,'连接服务器失败');
	        }

	        if($this->pg->update('game_param',$data,$where)) {
	        	// 记录到日志表
	        	$content = array('server_id'=>$zone_id,
	        		'name'=>$name,
	        		'new_value'=>$value,
	        		'old_value'=>$_POST['old'] 
	        		);
	        	// $log = array(
	        	// 	'action_type'=>'game_param',
	        	// 	'action_uid'=>$this->session->userdata('user_id'),
	        	// 	'action_uname'=>$this->session->userdata('username'),
	        	// 	'action_content'=>serialize($content),
	        	// 	'action_time'=>time()
	        	// 	);
	        	// $this->db->insert('activity_actions',$log);	
	        	show_response(0,'更新成功');
	        } else {
	        	// echo $this->pg->last_query();
	        	show_response(1,'更新失败');
	        }
		}

		show_response(1,'参数非法');
		
	}

	/**
	 * 动态获取合作方下的游戏
	 * @return json 游戏列表
	 */
	public function ajax_get_game()
	{
		$id = isset($_POST['id'])?$_POST['id']:'';

		$games= array();

		$partner = $this->server_model->get_one_partner($id);

		if(!empty($partner))
		{
			$gameIDArr = explode(',',$partner['game_id']);

			if(is_array($gameIDArr))
			{
				foreach($gameIDArr as $gameID)
				{
					$games[]=$this->server_model->get_one_game($gameID);
				}
			}
		}
		echo json_encode($games);
	}

	/**
	 * [ajax_get_bigzone 动态获取某一渠道的所有大区]
	 * @return [json] [大区列表]
	 */
	public function ajax_get_bigzone()
	{
		$id = isset($_POST['id'])?$_POST['id']:'';

		$bigzones = $this->server_model->get_all_bigzone(array('bigzone.channel_id'=>$id));

		echo json_encode($bigzones);

	}


	/****************************版本管理开始********************************/
	
	//版本列表
	public function versions()
	{
		// 取得当前可用渠道

		$channels = $this->server_model->get_all_channels();

		$this->data['channels'] = $channels;

        // if(!empty($channels))
        // {
        //     $channel_ids = array_keys($channels,'channel_id');
            
        //     if(!empty($channel_ids)) {
        //          $this->db->where_in('channel_version.channel_id',$channel_ids);
        //     }    
        // }

		// if(isset($_POST['add']))
		// {
		// 	unset($_POST['add']);
		// 	$this->save_version($_POST);
		// }


		$this->data['page_title'] = '版本管理';

		$this->data['versions'] = $this->server_model->get_all_versions();
		$this->load->view('server/version_lists',$this->data);
	}

	public function version_add()
	{
		$channel_ids = $this->input->post('channel');
		$version_no = $this->input->post('no');

		// 处理版本号 转换为 2014080801.001格式
		$arr = explode('.',$version_no);

		$version_no = sprintf('%8d.%03d.%03d',$arr[0],isset($arr[1])?$arr[1]:000,isset($arr[2])?$arr[2]:000);

		$type = $this->input->post('update_type');

		$content = $this->input->post('content');

		$debug = $this->input->post('debug');

		$file_url = $this->input->post('url');

		$file_size = $this->input->post('size');

		$file_md5 = $this->input->post('md5');

		if($type == 3 && $file_url == "") {
			show_response(1,'文件链接非法');
		}

		if(!empty($channel_ids)) {

			$data = array(
			'version_no'=>$version_no,
			'resource_url'=>$file_url,
			'resource_size'=>$file_size,
			'resource_md5'=>$file_md5,
			'create_time'=>time(),
			'type'=>$type,
			'content'=>$content,
			'is_debug'=>$debug=='on'?1:0
			);

			$version_id = $this->server_model->insert_version($data);

			if(!empty($version_id)) {
				foreach ($channel_ids as $channel_id) {
					$this->db->insert('channel_version',array('version_id'=>$version_id,'channel_id'=>$channel_id));
				}
				show_response(0,'添加成功');
			} else {
				show_response(1,'添加失败');
			}

		} else {
			show_response(1,'参数非法');
		}
		
	}

	private function save_version($data)
	{
		// parse_str($data,$arr);
		if(!empty($data))
		{

			//验证参数
			if ($id = $this->server_model->insert_version($data))
			{
				//获取渠道
				$channels = $data['channel'];

				if(!empty($id))
				{
					foreach ($channels as $channel) {
						$this->db->insert('channel_version',array('channel_id'=>$channel,'version_id'=>$id));
					}
				}
				return is_numeric($id);
			} else {
				return FALSE;
			}
		}
		
	}

	// 版本更新ajax
	public function version_update()
	{
		$data = array($_POST['name']=>$_POST['value']);
		$where = array('version_id'=>$_POST['pk']);
		
		if(!$this->server_model->update_version($data,$where))
		{
			show_response(1,'更新失败');
		} else {
			show_response(0,'更新成功');
		}	
	}

	/**
	 * [删除版本](2014-08-08)
	 * @return [type] [description]
	 */
	public function version_delete()
	{
		$id = isset($_POST['id'])?$_POST['id']:'';
		$ret = array();
		$ret['code'] = 1;

		if(!empty($id))
		{
			if($this->server_model->delete_version($id))
			{
				$ret['code'] = 0;
			} else {
				$ret['msg'] = '删除失败';	
			}			
		} else {
			$ret['msg'] = '参数为空';
		}

		echo json_encode($ret);
	}

	
	/****************************版本管理结束********************************/

	public function mall()
	{

		$this->data['page_title'] = '商城管理';

		$this->data['zones'] = $this->server_model->get_all_zones();
		$this->load->view('server/mall_lists',$this->data);
	}

	public function mall_add()
	{
		$this->load->model('pg_model');

		// 获取区服列表
		$zone_ids = $this->input->post('zone');

		$item_id = $this->input->post('item_id');

		$max_buy= $this->input->post('max_buy');
		$vip_level= $this->input->post('vip_level');

		$shop_type = $this->input->post('shop_type');
		$tag_type = $this->input->post('tag_type');
		$price_type = $this->input->post('price_type');

		$price_old = $this->input->post('price_old');
		$price_new = $this->input->post('price_new');

		// 开始时间
		$begin_time = $this->input->post('begin_time');
		// 结束时间
		$end_time = $this->input->post('end_time');
	
		$order_id = $this->input->post('order');


		if(is_array($zone_ids)&&count($zone_ids)>0) {
		
			foreach ($zone_ids as $zone_id) {

				$this->pg = $this->pg_model->connect($zone_id);

	            if(!$this->pg)
	            {
	            	show_response(1,$zone_id.'服务器连接失败,请检查设置后重新添加！');
	            }

            	if(!empty($item_id)) {
            		$this->pg->where('shop_type',$shop_type);
            		$this->pg->where('item_id',$item_id);
            		$this->pg->where('price_type',$price_type);
            		$query = $this->pg->get('time_limit_mall');
            		$item = $query->row_array();

            		if(!empty($item)) {
            			$this->pg->where('shop_type',$shop_type);
            			$this->pg->where('item_id',$item_id);
            			$this->pg->where('price_type',$price_type);

            			$data = array(
		            		'order_id'=>intval($order_id),
		            		'begin_time'=>strtotime($begin_time),
		            		'end_time'=>strtotime($end_time),
		            		'vip_level'=>intval($vip_level),
		            		'max_level'=>999,
		            		'min_level'=>0,
		            		'max_buy'=>intval($max_buy),
		            		'price_old'=>intval($price_old),
		            		'price_new'=>intval($price_new),
		            		'tag_type'=>intval($tag_type),
		            		'status'=>0,
		            	);

		            	$this->pg->update('time_limit_mall',$data);
            		} else {
            			// 插入
		            	$data = array(
		            		'item_id'=>$item_id,
		            		'shop_type'=>intval($shop_type),
		            		'order_id'=>intval($order_id),
		            		'begin_time'=>strtotime($begin_time),
		            		'end_time'=>strtotime($end_time),
		            		'vip_level'=>intval($vip_level),
		            		'max_level'=>999,
		            		'min_level'=>0,
		            		'max_buy'=>intval($max_buy),
		            		'price_old'=>intval($price_old),
		            		'price_new'=>intval($price_new),
		            		'price_type'=>intval($price_type),
		            		'tag_type'=>intval($tag_type),
		            		'status'=>0,
		            	);

		            	$this->pg->insert('time_limit_mall',$data);
            		}	            	
	            	//echo $this->pg->last_query();
            	}      	
			}
			show_response(0,'设置成功');

		} else {
			show_response(1,'参数非法');
		}
	}

	// 查询商城
	public function mall_query()
	{
		$zone_id = $this->input->post('zone');
		$begin_time = $this->input->post('from');
		$end_time = $this->input->post('to');

		$shop_type = $this->input->post('type');

		if(!empty($zone_id)) {

			$this->load->model('pg_model');

			$this->pg = $this->pg_model->connect($zone_id);

			if(!$this->pg) {
                show_response(1,'连接服务器失败');
            }
       
            if(!empty($type)) {
            	$this->pg->where('shop_type',$shop_type);
            }

            if(!empty($begin_time)&&!empty($end_time)) {
            	$this->pg->where('begin_time <',strtotime($begin_time));
            	$this->pg->where('end_time >',strtotime($end_time));
            }

            $query = $this->pg->get('time_limit_mall');
            // echo $this->pg->last_query();

            $data = $query->result_array();

            // // 处理显示
            // foreach ($data as $kk=>$vv) {
            // 	if(isset($keys[$vv['k']])&&!empty($keys[$vv['k']])) {
            // 		$data[$kk]['k'] = $vv['k'].'('.$keys[$vv['k']].')';
            // 	}
            // }

            echo json_encode(array('code'=>0,'data'=>$data));

		} else {
			show_response(1,'区服不得为空');
		}
	}

	public function mall_update()
	{
		$name = $_POST['name'];
		$value = $_POST['value'];
		// 转换为时间戳
		if($name=='begin_time'||$name=='end_time') {
			$value = strtotime($value);
		}

		$data = array($name=>$value,'status'=>0);
		
		$zone_id = $_POST['zone'];

		$shop_type = $this->input->post('shop_type');
		$item_id = $this->input->post('item_id');
		$price_type = $this->input->post('price_type');

		if(!empty($zone_id)&&$shop_type&&$item_id&&$price_type) {

			$this->load->model('pg_model');
			$this->pg = $this->pg_model->connect($zone_id);

			if(!$this->pg) {
	            show_response(1,'连接服务器失败');
	        }

	        $this->pg->where('item_id',$_POST['item_id']);
	        $this->pg->where('shop_type',$_POST['shop_type']);
	        $this->pg->where('price_type',$_POST['price_type']);

	        if($this->pg->update('time_limit_mall',$data)) {
	        	// echo $this->pg->last_query();
	        	// 记录到日志表
	        	// $content = array('server_id'=>$zone_id,
	        	// 	'name'=>$name,
	        	// 	'new_value'=>$value,
	        	// 	'old_value'=>$_POST['old'] 
	        	// 	);
	        	// $log = array(
	        	// 	'action_type'=>'game_param',
	        	// 	'action_uid'=>$this->session->userdata('user_id'),
	        	// 	'action_uname'=>$this->session->userdata('username'),
	        	// 	'action_content'=>serialize($content),
	        	// 	'action_time'=>time()
	        	// 	);
	        	// $this->db->insert('activity_actions',$log);	
	        	show_response(0,'更新成功');
	        } else {
	        	// echo $this->pg->last_query();
	        	show_response(1,'更新失败');
	        }
		}

		show_response(1,'参数非法');
		
	}

		// 活动删除
	public function mall_delete()
	{
		$zone_id = $this->input->post('zone');
		$shop_type = $this->input->post('shop_type');
		$price_type = $this->input->post('price_type');
		$item_id = $this->input->post('item_id');

		if($zone_id&&$shop_type&&$price_type&&$item_id) {

			$this->pg = $this->pg_model->connect($zone_id);

			if($this->pg) {
				// 删除
		       	$this->pg->where('shop_type',$shop_type);
		       	$this->pg->where('item_id',$item_id);
		       	$this->pg->where('price_type',$price_type);

		            	
		        if($this->pg->update('time_limit_mall',array('status'=>3))) {
		        	show_response(0,'删除成功');
		        } else {
		        	show_response(1,'删除失败');
		        }

		        // echo $this->pg->last_query();

		        // $sql = sprintf("SELECT FROM game_param_%s WHERE k='%s' and v='%s'",$zone_id,$k,$v);
		            	
		        // $ret = $this->pg->query($sql);


			}
			show_response(1,'服务器连接失败');

		}

		show_response(1,'参数非法');

	}

	public function review()
	{
		$this->_data['page_title'] = '游戏服列表';

		$this->_data['all_zones'] = $this->server_model->get_all_zones();
	
		$this->_data['zones'] = $this->server_model->get_all_review_zones();

		$this->load->view('server/review_lists',$this->_data);
	}


	public function review_add()
	{
		$zones = $this->input->post('zone');
		$version = $this->input->post('version');
		$begin_time = $this->input->post('begin_time');
		$end_time = $this->input->post('end_time');

		if(!empty($zones)) {
			foreach ($zones as $key => $zone) {
				$data = array(
					'zone_id'=>$zone,
					'min_version'=>$version,
					'begin_time'=>strtotime($begin_time),
					'end_time'=>strtotime($end_time)
					);

				$this->db->replace('zone_review',$data);
			}

			show_response(0,'添加成功');
		} else {
			show_response(1,'参数非法');
		}
	}

}

/* End of file server.php */
/* Location: ./application/controllers/server.php */