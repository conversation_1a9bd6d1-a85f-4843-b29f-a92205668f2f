<?php
/**
 * 阿里云短信服务测试脚本
 * 用于测试短信发送功能是否正常
 */

require_once('AliyunSms.class.php');

// 加载配置
$sms_config = include('sms_config.php');

// 检查配置是否已修改
if ($sms_config['accessKeyId'] == 'YOUR_ACCESS_KEY_ID') {
    echo "错误：请先修改 sms_config.php 中的配置信息\n";
    exit(1);
}

// 初始化短信服务
$aliyunSms = new AliyunSms($sms_config);

// 测试参数
$testPhone = '13800138000'; // 请替换为您的测试手机号
$templateCode = $sms_config['templateCode'];
$code = rand(1000, 9999);

echo "开始测试阿里云短信服务...\n";
echo "手机号: $testPhone\n";
echo "模板CODE: $templateCode\n";
echo "验证码: $code\n";
echo "签名: " . $sms_config['signName'] . "\n";
echo "------------------------\n";

// 发送短信
$result_json = $aliyunSms->templateSMS('', $testPhone, $templateCode, $code);
$result_obj = json_decode($result_json);

echo "响应结果:\n";
echo $result_json . "\n";
echo "------------------------\n";

if ($result_obj->resp->respCode == "000000") {
    echo "✓ 短信发送成功！\n";
    echo "请检查手机 $testPhone 是否收到验证码短信\n";
} else {
    echo "✗ 短信发送失败\n";
    echo "错误码: " . $result_obj->resp->respCode . "\n";
    echo "错误信息: " . $result_obj->resp->respMsg . "\n";
    
    echo "\n常见问题排查:\n";
    echo "1. 检查AccessKey ID和Secret是否正确\n";
    echo "2. 检查短信签名是否已审核通过\n";
    echo "3. 检查短信模板是否已审核通过\n";
    echo "4. 检查账户余额是否充足\n";
    echo "5. 检查手机号码格式是否正确\n";
}

echo "\n测试完成。\n";
?>
