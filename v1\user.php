<?php
require(APPPATH . '/libraries/REST_Controller.php');
require_once('AliyunSms.class.php');


class User extends REST_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->model('player_model');
        $this->player_model->set_database($this->_channel['game_id']);
        $this->load->model('user_model');
        $this->load->model('server_model');
    }

    // 推荐码绑定
    public function bind_post()
    {
        // 推荐吗ID
        $ruid = $this->input->post('ruid');
        // 申请人
        $uid = $this->input->post('uid');

        // 松阳麻将暂不开放
        if ($this->_channel['game_id'] == 9 || $this->_channel['game_id'] == 15) {
            $this->response(array('code' => 1, 'msg' => '礼包兑换功能暂未开放'));
        }

        if ($ruid == $uid) {
            $this->response(array('code' => 1, 'msg' => '推荐人不能为自己'));
        }

        // 查询操作用户是否存在
        $role = $this->player_model->get_role_info($uid);

        if (empty($role)) {
            $this->response(array('code' => 1, 'msg' => '玩家不存在'));
        }

        // 是否绑定
        if ($role['SpreaderID'] > 0) {
            $this->response(array('code' => 1, 'msg' => '已绑定，请勿重复绑定'));
        }

        // 查询代理对应的游戏玩家是否存在
        $role2 = $this->player_model->get_role_info($ruid);

        if (empty($role2)) {
            $this->response(array('code' => 1, 'msg' => '推荐码无效（用户不存在）'));
        }

        // 查询推荐码是否为代理
        $agent = $this->user_model->get_one_agent($ruid, $this->_channel['game_id']);

        if (empty($agent)) {
            $this->response(array('code' => 1, 'msg' => '推荐码无效（未开通代理资格）'));
        }

        if($this->_channel['game_id'] == 59){

            // 判断是否绑定了玩家
            $spreaders = $this->player_model->get_role_spreader($uid);

            if($spreaders) {
                $this->response(array('code' => 1, 'msg' => '推荐码无效（已绑定玩家）'));
            }

            $data = array(
                'role_id' => $uid,
                'game_id' => $this->_channel['game_id'],
                'agent_id' => $ruid,
                'is_play' => $role['PlayTimeCount'] > 0 ? 1 : 0,
                'bind_time' => time()
            );

            $this->db->insert('role_bind', $data);

            $this->response(array('code' => 0, 'msg' => '绑定成功', 'ruid' => (int)$ruid,'rname'=>$role2['NickName']));

        } else {
            // 查询自己是否为代理
            $agent2 = $this->user_model->get_one_agent($uid, $this->_channel['game_id']);

            if ($agent2) {
                // 获取是否有下级代理
                $children = $this->user_model->get_children_agent($agent2['id'], 1);

                if (count($children) > 0) {
                    $this->response(array('code' => 1, 'msg' => '推荐码无效（已存在下级代理）'));
                }
            }

            // 更新角色
            $this->player_model->update_role_info($uid, array('SpreaderID' => $ruid,'SpreaderTime'=> date('Y-m-d H:i:s')));

            $role = $this->player_model->get_role_info($uid);

            if ($role['SpreaderID'] == $ruid) {
                $score = $this->player_model->get_role_score($uid);
                if(in_array($this->_channel['game_id'],array(37,55,57))){
                    $this->player_model->update_role_score($uid, 20);

                    // 插入记录
                    $data = array(
                        'a_id' => 1,
                        'a_name' => 'admin',
                        'b_id' => $uid,
                        'b_name' => $role['NickName'],
                        'score' => 20,
                        'type' => 4,
                        'is_order' => 0,
                        'remark' => '绑定 赠送',
                        'game_id' => $this->_channel['game_id'],
                        'create_time' => date('Y-m-d H:i:s')
                    );
                }
                else{
                    $this->player_model->update_role_score($uid, 10);

                    // 插入记录
                    $data = array(
                        'a_id' => 1,
                        'a_name' => 'admin',
                        'b_id' => $uid,
                        'b_name' => $role['NickName'],
                        'score' => 10,
                        'type' => 4,
                        'is_order' => 0,
                        'remark' => '绑定 赠送',
                        'game_id' => $this->_channel['game_id'],
                        'create_time' => date('Y-m-d H:i:s')
                    );
                }

                $this->db->insert('recharges', $data);


                if ($agent2) { //设置为上级代理
                    $this->user_model->update_agent_info($agent2['id'], array('spreader_id', $agent['id']));
                }

                $data = array(
                    'role_id' => $role['UserID'],
                    'game_id' => $this->_channel['game_id'],
                    'agent_id' => $agent['id'],
                    'is_play' => $role['PlayTimeCount'] > 0 ? 1 : 0,
                    'bind_time' => time()
                );

                $this->db->insert('role_bind', $data);

                $this->response(array('code' => 0, 'msg' => '绑定成功', 'ruid' => (int)$ruid, 'insure' => (int)$score['RoomCard'],'rname'=>$role2['NickName']));
            } else {
                $this->response(array('code' => 1, 'msg' => '绑定失败，请重试'));
            }
        }



    }


    // 用户注册接口
    public function reg_post()
    {

//        $app_version = $this->input->get('app_version');
        $c_version = $this->input->post('c_version');
        $res_version = $this->input->post('res_version');

        $version_no = (int)$c_version . '.' . (int)$res_version;

        $uuid = $this->input->post('uuid');//唯一标识符

        $game = $this->server_model->get_one_game($this->_channel['game_id']);

        $data = array();

/*
        if(in_array($this->_channel['game_id'],array(36))) {
            $data['login_server'] = 'lhmj-s1.tuo3.com.cn';
        } 
        if(in_array($this->_channel['game_id'],array(20,54,37,32,55))) {
            $data['login_server'] = 'lhmj.tuo3.com.cn';
             }
             */

//31-40:            
//$data['login_server'] = '121.43.186.109';
//$data['login_server'] = '121.43.160.89';
//$data['login_server'] = '121.43.162.164';
//$data['login_server'] = '114.55.124.214';
//$data['login_server'] = '121.43.161.228';
//$data['login_server'] = '121.43.161.115';
//$data['login_server'] = '121.43.166.46';
//$data['login_server'] = '121.43.165.103';
//$data['login_server'] = '114.55.124.251';
//$data['login_server'] = '121.43.165.15';
    
    	
    	$data['login_server'] = '**************';
    	
       // if(in_array($this->_channel['game_id'],array(37,32,54,36))) {
        //    $data['login_server'] = '**************';
        //}
        //if(in_array($this->_channel['game_id'],array(20,55))) {
        //   $data['login_server'] = '**************';
        //}

        $this->server_model->set_database($game['game_id']);
        $shuffle_setting = $this->server_model->get_shuffle_card_setting();

        $shuffle_rate = 3;

        if($shuffle_setting) {
            $shuffle_rate = $shuffle_setting['StatusValue']*1;
        }

        // 判断IP是否在白名单内
        $ip_arr = explode(',', $this->_channel['ip_whitelist']);

        $data['is_open_giftbag'] = $game['is_open_giftbag'] * 1;
        $data['is_open_mall'] = $game['is_open_mall'] * 1;
        $data['is_open_wheel'] = $game['is_open_wheel'] * 1;
        $data['is_open_location'] = $game['is_open_location'] * 1;
        $data['is_open_invite'] = $game['is_open_invite'] * 1;
        $data['is_open_envelop'] = $game['is_open_envelop'] * 1;
        $data['is_open_guess'] = $game['is_open_guess'] * 1;
        $data['is_open_my_red_bag'] = $game['is_open_my_red_bag'] * 1;
        $data['is_open_shuffle'] = $game['is_open_shuffle'] * 1;
        $data['is_open_red_bag'] = $game['is_open_red_bag'] * 1;
        $data['shuffle_rate'] = $shuffle_rate;

        if (!empty($uuid)){
            //获取uuid
            $where['uuid'] = $uuid;
            //查找后台配置
            $config = $this->server_model->get_server_config_by_uuid($where['uuid']);
            //如果已配置
            if (!empty($config)){
                if (!empty($config['user_id'])){
                    $data['user_id'] = (int)$config['user_id'];
                }
                unset($config);
                $server = $this->server_model->get_one_server_config($where);
                if($server){
                    $data['login_server'] = $server['host_address'];
                    $data['login_port'] = (int)$server['host_port'];
                    // 测试服返回测试后台
                    if($server['type'] == 1 && !empty($server['host_url'])) {
                        $data['host_url'] = $server['host_url'];
                    }
                }
            }else{
                unset($where['uuid']);
                $where['channel_id'] = $this->_channel['channel_id'];
                $where['version_no'] = $version_no;
                //查找后台配置
                $server = $this->server_model->get_one_server_config($where);
                //如果已配置
                if (!empty($server)){
                    $data['login_server'] = $server['host_address'];
                    $data['login_port'] = (int)$server['host_port'];
                    // 测试服返回测试后台
                    if($server['type'] == 1 && !empty($server['host_url'])) {
                        $data['host_url'] = $server['host_url'];
                    }
                }
            }
        }else{
            // 获取服务器配置
            $server = $this->server_model->get_one_server_config(
                array(
                    'version_no' => $version_no,
                    'channel_id' => $this->_channel['channel_id']
                )
            );

            if ($server) {
                $data['login_server'] = $server['host_address'];
                $data['login_port'] = $server['host_port'] * 1;
            }
        }

        // IP白名单下默认进入到测试服
//        if ($this->_channel['update_model'] == 0 && $ip_arr && in_array($this->getIp(), $ip_arr)) {
//            $data['login_server'] = '*************';
//            $data['login_port'] = 8603;
//            $data['is_open_giftbag'] = 1;
//            $data['is_open_mall'] = 1;
//            $data['is_open_wheel'] = 1;
//            $data['is_open_location'] = 1;
//            $data['is_open_invite'] = 1;
//            $data['is_open_envelop'] = 1;
//            $data['is_open_guess'] = 1;
//            $data['is_open_my_red_bag'] = 1;
//            $data['is_open_shuffle'] = 1;
//            $data['is_open_red_bag'] = 1;
//        }

        $data['is_audit'] = 0;
        $data['is_relax'] = $this->_channel['is_relax']*1;

        if ($this->_channel['is_audit'] == 1) {
            if ($this->_channel['audit_version'] <= $version_no) {
                $data['is_audit'] = 1;
                $data['login_server'] = 'mjtest.tuo3.com.cn';
                if(in_array($this->_channel['game_id'],array(20,32,36,37,54,55,59))) {
                    $data['login_port'] = 8602;
                } else {
                    $data['login_port'] = 8605;
                }
                $data['is_open_mall'] = 0;
                $data['is_open_giftbag'] = 0;
                $data['is_open_invite'] = 0;
                $data['is_open_location'] = 0;
                $data['is_open_wheel'] = 0;
                $data['is_open_envelop'] = 0;
                $data['is_open_guess'] = 0;
                $data['is_open_my_red_bag'] = 0;
                $data['is_open_shuffle'] = 0;
                $data['is_open_red_bag'] = 0;
            }
        }

        if($this->getIp() == '**************') {
            $data['is_open_envelop'] = 1;
            $data['is_open_wheel'] = 1;
        }

        if($this->_channel['game_id'] == 30) {
            if($this->_channel['channel_id']!=21010005) {
                $data['login_server'] = '*************';
                $data['login_port'] = 8602;
                $data['host_url'] = 'http://**************/admin';
            }
        }

        if ($game['game_id'] == 20) {
        	// 有系登陆8601用
        	$data['login_server'] = 'lhmj-tz.tuo3.com.cn';
            //$data['login_server'] = 'lhmj-tz.tuo3.com.cn';
            // $data['login_server'] = 'lhmj-tzg1.tuo3.com.cn';
            // $data['login_server'] = 'lhmj-tzg2.tuo3.com.cn';
            // $data['login_server'] = 'lhmj-tzraw.tuo3.com.cn';
            
            
            //$data['login_server'] = '**************';
        	if($this->getIp() == "***************"){
			// $data['login_server'] = 'lhmj-test.tuo3.com.cn';
        	}
        }

        if ($game['game_id'] == 37) {
            $data['login_server'] = 'lhmj-lh.tuo3.com.cn';
        }

        if ($game['game_id'] == 54) {
            $data['login_server'] = 'lhmj-pa.tuo3.com.cn';
            //$data['login_server'] = '**************';
        }

        // 获取公告
        $notice = $this->server_model->get_latest_notice($this->_channel['channel_id'], 1);
        $data['notice'] = $notice ? $notice['content'] : '';

        $rollmsg = $this->server_model->get_latest_notice($this->_channel['channel_id'], 2);
        // 获取滚动消息
        $data['rollmsg'] = $rollmsg ? $rollmsg['content'] : '';

        if ($this->_channel['update_model'] == 1 && $ip_arr && in_array($this->getIp(), $ip_arr)) {
            $data['client_version'] = $this->_channel['test_client_version'] * 1;
            $data['res_version'] = $this->_channel['test_hall_res_version'] * 1;
            $data['update_content'] = $this->_channel['test_update_content'];
            $data['is_restart'] = $this->_channel['test_is_restart'] * 1;

        } else {
            $data['client_version'] = $this->_channel['client_version'] * 1;
            $data['res_version'] = $this->_channel['hall_res_version'] * 1;
            $data['update_content'] = $this->_channel['update_content'];
            $data['is_restart']  = $this->_channel['is_restart'] * 1;
        }


        $data['download_url'] = "https://lhmj.tuo3.com.cn/public/" . $this->_channel['channel_id'] . '/' . $data['client_version'] . '_' . $data['res_version'];
        $data['package_url'] = $this->_channel['homepage'];

        $gamelist = array();

        // 获取游戏类型
        $types = $this->server_model->get_game_type($this->_channel['game_id']);
        
        //log_message("error","get_game_type");
        //log_message("error",$this->_channel['game_id']);

        foreach ($types as $key => $type) {

            $gamelist[$key]['type_id'] = $type['type_id'] * 1;

            $update_model = $this->_channel['update_model'] == 1 && $ip_arr && in_array($this->getIp(), $ip_arr) ? 1 : 0;

            $version = $this->server_model->get_channel_version($this->_channel['channel_id'], $type['type_id'], $update_model);

            if ($version) {
                $gamelist[$key]['res_version'] = $version['version_no'] * 1;
            } else {
                $gamelist[$key]['res_version'] = 0;
            }

            $gamelist[$key]['module_name'] = $type['module_name'];
            $gamelist[$key]['sort_id'] = $type['sort'] * 1;

            // 获取每个类型下的玩法
            $kinds = $this->server_model->get_game_kinds($this->_channel['game_id'], $type['type_id']);

            $arr2 = array();

            foreach ($kinds as $key2 => $kind) {
                $arr2[$key2]['kind_id'] = $kind['kind_id'] * 1;
                $arr2[$key2]['kind_name'] = $kind['kind_name'];
                $arr2[$key2]['sort_id'] = $kind['sort'] * 1;
            }

            $gamelist[$key]['kindlist'] = $arr2;

        }

        $data['gamelist'] = $gamelist;

        $linklist = array();

        $links = $this->server_model->get_game_links($this->_channel['game_id']);

        foreach ($links as $key=>$link) {
            $linklist[$key]['link_id'] = $link['link_id']*1;
            $linklist[$key]['link_name'] = $link['name'];
            $linklist[$key]['link_url'] = base_url().'link/index/'.$link['link_id'];
            $linklist[$key]['sort_id'] = $link['sort']*1;
        }

        $data['linklist'] = $linklist;

//        // 检查是否未审核状态
//        if($this->_channel['is_audit'] == 1) {
//            // TODO 判断审核版本号
//            if($this->_channel['audit_version'] == substr($version_no,0,8)) {
//                $this->response(array('code'=>0,'msg'=>'版本审核中','notice'=>'','rollmsg'=>'','activities'=>0,'host_address'=>'*************','host_port'=>10086),200);
//            }云之讯短信服务异常
//        }

        $this->response(array('code' => 0, 'msg' => '', 'data' => $data));

    }

    public function getIp()
    {

        if (!empty($_SERVER["HTTP_CLIENT_IP"])) {
            $cip = $_SERVER["HTTP_CLIENT_IP"];
        } else if (!empty($_SERVER["HTTP_X_FORWARDED_FOR"])) {
            $cip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        } else if (!empty($_SERVER["REMOTE_ADDR"])) {
            $cip = $_SERVER["REMOTE_ADDR"];
        } else {
            $cip = '';
        }
        preg_match("/[\d\.]{7,15}/", $cip, $cips);
        $cip = isset($cips[0]) ? $cips[0] : 'unknown';
        unset($cips);

        return $cip;
    }

    public function get_verify_code_post()
    {
        // 判断用户是否绑定
        $role_id = $this->input->post('uid');
        $phone = $this->input->post('phone');
        $type = $this->input->post('type')?$this->input->post('type'):'bind';

        if($type == 'bind') {
            $role = $this->player_model->get_role_info($role_id);

            if (empty($role)) {
                $this->response(array('code' => 1, 'msg' => '角色不存在'));
            }
        }

        // 加载阿里云短信配置
        $sms_config = include('sms_config.php');

        // 初始化阿里云短信服务
        $aliyunSms = new AliyunSms($sms_config);

        $templateCode = $sms_config['templateCode'];
        $code = rand(1000, 9999);

        // 发送短信（保持与原有接口兼容）
        $result_json = $aliyunSms->templateSMS('', $phone, $templateCode, $code);

        $result_obj = json_decode($result_json);

        if ($result_obj->resp->respCode == "000000") {
            $data = array(
                'role_id' => $role_id,
                'game_id' => $this->_channel['game_id'],
                'create_time' => time(),
                'phone' => $phone,
                'type' => $type,
                'code' => $code
            );
            $this->db->insert('tuo3_verify_code', $data);

            $this->response(array('code' => 0, 'msg' => '发送成功'));

        } else {
            $this->response(array('code' => 1, 'msg' => '发送失败：' . $result_obj->resp->respCode));
        }


    }

    public function phone_login_post() {
        $phone = $this->input->post('phone');
        $code = $this->input->post('code');

        $this->db->where('code', $code);
        $this->db->where('phone', $phone);
        $this->db->where('type', 'login');
        $query = $this->db->get('tuo3_verify_code');
        $row = $query->row_array();

        if (!$row) {
            $this->response(array('code' => 1, 'msg' => '验证码不存在'));
        }

        if ($row['status'] != 0) {
            $this->response(array('code' => 1, 'msg' => '验证码已使用'));
        }

        if ($row['create_time'] + 600 < time()) {
            $this->response(array('code' => 1, 'msg' => '验证码已失效'));
        }

        // 根据手机号查找用户
        $role = $this->player_model->get_role_info_by_phone($phone);

        if($role) {

            $this->db->where('id',$row['id']);
            $this->db->update('tuo3_verify_code',array('status'=>1));

            $this->response(array('code' => 0, 'msg' => '','data'=>array('bind'=>1,'account'=>$role['UserUin'],'gender'=>$role['Gender'],'nick'=>$role['NickName'],'avatar_url'=>$role['HeadHttp'],'phone'=>$phone,'code'=>$code)));
        } else {
            $this->response(array('code' => 0, 'msg' => '','data'=>array('bind'=>0,'phone'=>$phone,'code'=>$code)));
        }

    }

    public function bind_phone_post()
    {
        log_message("error","用户绑定请求参数");
        log_message("error",var_export($_POST,TRUE));

        $phone = $this->input->post('phone');
        $code = $this->input->post('code');
        $role_id = $this->input->post('uid');
        $type = $this->input->post('type')?$this->input->post('type'):'bind';

        $role = $this->player_model->get_role_info($role_id);

        if (empty($role)) {
            $this->response(array('code' => 1, 'msg' => '角色不存在'));
        }

        // 判断验证码是否有效
        $this->db->where('code', $code);
        $this->db->where('phone', $phone);
        $this->db->where('type', $type);
        $query = $this->db->get('tuo3_verify_code');
        $row = $query->row_array();

        if (!$row) {
            $this->response(array('code' => 1, 'msg' => '验证码不存在'));
        }

        if ($row['status'] != 0) {
            $this->response(array('code' => 1, 'msg' => '验证码已使用'));
        }

        if ($row['create_time'] + 600 < time()) {
            $this->response(array('code' => 1, 'msg' => '验证码已失效'));
        }

        $this->db->where('id',$row['id']);
        $this->db->update('tuo3_verify_code',array('status'=>1));

        if($type == 'bind') {


//            $is_bind = false;
//
//            if ($role['RegisterMobile']) {
//                $is_bind = true;
//            }

            // 清理掉之前绑定手机号的角色
            $this->player_model->clear_role_phone($phone);
                //绑定
            if ($this->player_model->update_role_info($role_id, array('RegisterMobile' => $phone))) {
                $score = $this->player_model->get_role_score($role_id);

                $add_score = 0;

                if ($role['MobileRewardGot'] == 0) {
                    $game = $this->server_model->get_game_by_id($this->_channel['game_id']);
                    $add_score = $game['phone_bind_prize'];
                    if ($add_score > 0) {
                        $this->player_model->update_role_score($role_id, $add_score);
                        $this->player_model->update_role_info($role_id, array('MobileRewardGot' => 1));
                    }
                }

                $this->response(array('code' => 0, 'msg' => '绑定成功', 'data' => array(
                    'phone' => $phone,
                    'total_fangka' => $score['RoomCard'] * 1,
                    'add_fangka' => $add_score * 1
                )));
            } else {
                $this->response(array('code' => 1, 'msg' => '绑定失败'));
            }
        } else if($type == 'login'){
            $this->player_model->update_role_info($role_id, array('RegisterMobile' => $phone));
            $this->response(array('code' => 0, 'msg' => '绑定成功','data'=>array('phone' => $phone)));
        }
    }

    //添加更新服务器配置
    public function insert_uuid_get()
    {
        $config['uuid'] = $this->input->get('uuid');

        if (!empty($config['uuid'])){
            $res = $this->server_model->get_uuid_data($config['uuid']);

            $data['uuid'] = $config['uuid'];
            if ($this->input->get('list_id')){
                $data['list_id'] = $this->input->get('list_id');
            }
            if ($this->input->get('user_id')){
                $data['user_id'] = $this->input->get('user_id');
            }

            if ($res){
                $this->server_model->update_server_config($res['config_id'],$data);
                $ret['msg'] = '更新成功';
                $this->response($ret,200);
            }else{
                $this->server_model->insert_server_config($data);
                $ret['msg'] = '添加成功';
                $this->response($ret,200);
            }
        }else{
            $ret['msg'] = '添加失败,uuid不能为空';
            $this->response($ret,200);
        }
    }

    //获取服务器列表
    public function get_server_get()
    {
        $ret['data'] = $this->server_model->get_server_list();
        $this->response($ret,200);
    }

    //删除服务器配置
    public function delete_server_uuid_get()
    {
        $uuid = $this->input->get('uuid');
        if (!empty($uuid)){
            if ($this->server_model->delete_uuid_data($uuid)){
                $ret['msg'] = '删除成功';
            }else{
                $ret['msg'] = '删除失败';
            }
        }else{
            $ret['msg'] = 'uuid请勿为空';
        }

        $this->response($ret,200);
    }
}

?>