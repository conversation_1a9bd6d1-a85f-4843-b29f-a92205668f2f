<?php
/**
 * Created by PhpStorm.
 * User: Jason.z<http://www.jason-z.com>
 * Date: 2017/6/7
 * Time: 下午2:42
 */

defined('BASEPATH') OR exit('No direct script access allowed');


class Ipaynow extends CI_Controller
{
    private $_appId = '150155930270551';
    private $_appSecret = 'FdhPfq8XNzVQH27VEsPSO7A7k8WMK5XA';
    private $_url = 'https://pay.ipaynow.cn';

    function __construct()
    {
        parent::__construct();
        $this->load->model('mp_model');
        $this->load->model('server_model');
        $this->load->model('player_model');
        $this->load->model('user_model');
    }


    public function create()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $good_id = $this->input->post('good_id');
        $role_id = $this->input->post('role_id');
        $channel_id = $this->input->post('channel_id');

        if(empty($good_id) || empty($role_id) || empty($channel_id)) {
            echo json_encode(array('status' => false, 'msg' => '参数非法'));exit;
        }


        $channel = $this->server_model->get_one_channel($channel_id);

        if(empty($channel)) {
            echo json_encode(array('status' => false, 'msg' => '渠道不存在'));exit;
        }

        $this->player_model->set_database($channel['game_id']);

        // 查询角色是否存在
        $role = $this->player_model->get_role_info($role_id);

        if(empty($role)) {
            echo json_encode(array('status' => false, 'msg' => '角色不存在'));exit;
        }

        // 获取商品信息
        $good = $this->mp_model->get_mp_good_by_id($good_id);

        if(empty($good)) {
            echo json_encode(array('status' => false, 'msg' => '商品不存在'));
            exit;
        }

        //获取用户微信信息
        $mp_user = $this->mp_model->get_mp_user_by_roleid($role_id,$channel['game_id']);

        // 是否为代理
        $agent = $this->user_model->get_one_agent($role_id,$channel['game_id']);

//        if($good['type']==1) { // 普通玩家购买
//            if($agent) {
//                echo json_encode(array('status' => false, 'msg' => '商品信息不合法'));
//                exit;
//            }
//        } else {
//            if(!$agent) {
//                echo json_encode(array('status' => false, 'msg' => '商品信息不合法'));
//                exit;
//            }
//        }

        $this->load->helper('string');

        $order_no = date("YmdHis") . random_string('nozero', 3);

        if($role['SpreaderID'] <= 0 && !$agent && $channel['game_id'] != 30){
            $good['price'] = $good['price'] * 1.25;
        }

        if (empty($mp_user)){
            $mp_user['user_id'] = 0;
        }

        // 创建订单
        $data = array(
            'user_id' => $mp_user['user_id'],
            'role_id'  => $role_id,
            'agent_id'  => $agent?$agent['id']:0,
            'good_id'  => $good_id,
            'game_id'  => $channel['game_id'],
            'order_no' => $order_no,
            'is_first' => $good['is_first'],
            'is_agent' => ($good['type']-1),
            'total_fee' => $good['price'],
            'is_mall'  => 1,
            'kind_id'  =>$role['CommonKindID'],
            'create_time' => time()
        );

        $this->db->insert('mp_order', $data);

        $param['appId'] = $this->_appId;
        $param['deviceType'] = "0601";
        $param['frontNotifyUrl'] = "https://lhmj.tuo3.com.cn/wap3";
        $param['funcode'] = "WP001";
        $param['mhtCharset'] = "UTF-8";
        $param['mhtCurrencyType'] = "156";
        $param['mhtOrderAmt'] = $good['price'] * 100;
        $param['mhtOrderDetail'] = '房卡';
        $param['mhtOrderName'] = '房卡';
        $param['mhtOrderNo'] = $order_no;
        $param['mhtOrderStartTime'] = date('YmdHis');
        $param['mhtOrderTimeOut'] = 3600;
        $param['mhtOrderType'] = "01";
        $param['mhtReserved'] = $order_no;
        $param['mhtSignType'] = "MD5";
        $param['notifyUrl'] = base_url().'api/v1/ipaynow/notify';
        $param['outputType'] = 2;
        $param['payChannelType'] = 13;
        $param['version'] = "1.0.0";

        $str = '';

        foreach ($param as $k=>$v) {
            if($v) {
                $str .= $k.'='.$v.'&';
            }
        }

        $str .= md5($this->_appSecret);

//        var_dump($str);
//
//        echo PHP_EOL;

        $param['mhtSignature'] = md5($str);

//        var_dump(http_build_query($param));
//
//        echo PHP_EOL;

        $return = $this->_https_curl(http_build_query($param));

        parse_str($return,$result);

        if(isset($result['responseCode'])&&($result['responseCode'] == 'A001')) {
            echo json_encode(array('status' => true, 'msg' => '','tn'=> $result['tn']));
        } else {
            echo json_encode(array('status' => false, 'msg' => '下单错误'));
        }


    }

    private function _https_curl($data)
    {
        $curl = curl_init($this->_url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_HEADER ,0);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        $content = curl_exec($curl);
        curl_close($curl);

//        var_dump($content);

        return $content;
    }


    public function notify()
    {
        $data = file_get_contents('php://input');
        log_message("error","现在支付回调");
        log_message("error",$data);

        parse_str($data,$data);

        if(!$this->_check_sign($data)) {
            echo "签名验证失败";exit;
        }

        // 查找订单
        $this->db->where('order_no',$data['mhtOrderNo']);
        $query = $this->db->get('mp_order');
        $order = $query->row_array();

        if($order['status'] == 1) {
            echo "success=Y";exit;
        }

        log_message('error','现在支付id');
        log_message('error',$order['id']);

        if($data['transStatus'] == 'A001') {
            $this->db->where('id',$order['id']);
            $this->db->update('mp_order',array('transaction_id'=>$data['channelOrderNo'],'pay_time'=>time(),'status'=>1));

            $this->player_model->set_database($order['game_id']);

            // 获取商品信息
            $good = $this->mp_model->get_mp_good_by_id($order['good_id']);
            $role = $this->player_model->get_role_info($order['role_id']);

            //添加房卡
            $this->player_model->update_role_score($order['role_id'], $good['score']);

            if ($order['game_id'] == 30){
                // 查询是否为代理
                $adminer = $this->user_model->get_one_agent($order['role_id'], $order['game_id']);
            }

            // 获取推荐代理
            if ($role && $role['SpreaderID'] > 0) {

                $agent = $this->user_model->get_one_agent($role['SpreaderID'], $order['game_id']);

                if ($agent) {

                    $rate = 0.4;
                    if ($order['game_id'] == 35){
                        $rate = 0.45;
                    }
                    if ($order['game_id'] == 32){
                        $rate = 0.5;
                    }
                    if ($order['game_id'] == 30){
                        $rate = 0.376;
                    }

                    // 插入到收益表
                    $data = array(
                        'a_id' => $role['UserID'],
                        'a_account' => $role['Accounts'],
                        'b_id' => $agent['id'],
                        'b_account' => $agent['username'],
                        'create_time' => time(),
                        'type' => 4,
                        'money' => $rate * $order['total_fee'],
                        'district_id' => empty($agent['district_id'])?0:$agent['district_id']
                    );

                    $this->db->insert('bonus', $data);

                    $this->user_model->update_agent_bonus($agent['id'], $rate * $order['total_fee']);

                    // 获取上级代理
                    $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);

                    // 判断其下级代理个数
                    $count = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                    $rate = 0.06;

                    if ($order['game_id'] == 30){
                        $rate = 0.047;
                    }

                    // 插入到收益表
                    $data = array(
                        'a_id' => $agent['id'],
                        'a_account' => $agent['username'],
                        'b_id' => $parent['id'],
                        'b_account' => $parent['username'],
                        'create_time' => time(),
                        'type' => 1,
                        'money' => $rate * $order['total_fee'],
                        'district_id' => empty($agent['district_id'])?0:$agent['district_id']
                    );

                    $this->db->insert('bonus', $data);

                    $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

                    if ($parent['spreader_id'] > 0) {
                        $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                        // 判断其下级代理个数
                        $count2 = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                        $rate = 0.04;

                        if ($order['game_id'] == 30){
                            $rate = 0.0188;
                        }

                        // 插入到收益表
                        $data = array(
                            'a_id' => $agent['id'],
                            'a_account' => $agent['username'],
                            'b_id' => $parent2['id'],
                            'b_account' => $parent2['username'],
                            'create_time' => time(),
                            'type' => 2,
                            'money' => $rate * $order['total_fee'],
                            'district_id' => empty($agent['district_id'])?0:$agent['district_id']
                        );

                        $this->db->insert('bonus', $data);

                        $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);

                    }


                }

            }else if($adminer){
                $parent = $this->user_model->get_agent_by_id($adminer['spreader_id']);

                if(in_array($order['game_id'],array(30))) {
                    $rate = 0.376;
                }

                // 插入到收益表
                $data = array(
                    'a_id'=>$adminer['id'],
                    'a_account'=>$adminer['username'],
                    'b_id'=>$parent['id'],
                    'b_account'=>$parent['username'],
                    'create_time'=>time(),
                    'type'=>1,
                    'money'=>$rate*$order['total_fee'],
                    'district_id' => empty($adminer['district_id'])?0:$adminer['district_id']
                );

                $this->db->insert('bonus',$data);

                // 更新收益
                $this->user_model->update_agent_bonus($parent['id'],$rate*$order['total_fee']);

                // 获取上上级代理
                if($parent['spreader_id']>0) {
                    $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);

                    if(in_array($order['game_id'],array(30))) {
                        $rate = 0.047;
                    }

                    // 插入到收益表
                    $data = array(
                        'a_id'=>$adminer['id'],
                        'a_account'=>$adminer['username'],
                        'b_id'=>$parent2['id'],
                        'b_account'=>$parent2['username'],
                        'create_time'=>time(),
                        'type'=>2,
                        'money'=>$rate*$order['total_fee'],
                        'district_id' => empty($adminer['district_id'])?0:$adminer['district_id']
                    );

                    $this->db->insert('bonus',$data);

                    $this->user_model->update_agent_bonus($parent2['id'],$rate*$order['total_fee']);

                    if($parent2['spreader_id']>0) {
                        // 获取上上级代理
                        $parent3 = $this->user_model->get_agent_by_id($parent2['spreader_id']);

                        if(in_array($order['game_id'],array(30))) {
                            $rate = 0.0188;
                        }

                        // 插入到收益表
                        $data = array(
                            'a_id' => $adminer['id'],
                            'a_account' => $adminer['username'],
                            'b_id' => $parent3['id'],
                            'b_account' => $parent3['username'],
                            'create_time' => time(),
                            'type' => 3,
                            'money' => $rate * $order['total_fee'],
                            'district_id' => empty($adminer['district_id'])?0:$adminer['district_id']
                        );

                        $this->db->insert('bonus', $data);

                        $this->user_model->update_agent_bonus($parent3['id'], $rate * $order['total_fee']);
                    }
                }
            }




            // if ($adminer) {
            //     $this->player_model->set_database($order['game_id']);
            //     $this->player_model->update_role_score($order['role_id'], $good['score']);

            //     // 计算代理收益
            //     if ($order['game_id'] != 9 && $adminer['spreader_id'] > 0) {
            //         // 获取上级代理
            //         $parent = $this->user_model->get_agent_by_id($adminer['spreader_id']);
            //         // 判断其下级代理个数
            //         $count = count($this->user_model->get_children_agent($adminer['spreader_id'], 1));

            //         $rate = 0.00;

            //         if ($count < 3) {
            //             $rate = 0.08;
            //         } else {
            //             $rate = 0.15;
            //         }

            //         // 插入到收益表
            //         $data = array(
            //             'a_id' => $adminer['id'],
            //             'a_account' => $adminer['username'],
            //             'b_id' => $parent['id'],
            //             'b_account' => $parent['username'],
            //             'create_time' => time(),
            //             'type' => 1,
            //             'money' => $rate * $order['total_fee']
            //         );

            //         $this->db->insert('bonus', $data);

            //         // 更新收益
            //         $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

            //         // 获取上上级代理
            //         if ($parent['spreader_id'] > 0) {
            //             $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
            //             // 判断其下级代理个数
            //             $count2 = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

            //             if ($count2 >= 5) { //享受2级代理收益
            //                 $rate = 0.1;

            //                 // 插入到收益表
            //                 $data = array(
            //                     'a_id' => $adminer['id'],
            //                     'a_account' => $adminer['username'],
            //                     'b_id' => $parent2['id'],
            //                     'b_account' => $parent2['username'],
            //                     'create_time' => time(),
            //                     'type' => 2,
            //                     'money' => $rate * $order['total_fee']
            //                 );

            //                 $this->db->insert('bonus', $data);

            //                 $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);

            //             }

            //             if ($parent2['spreader_id'] > 0) {
            //                 // 获取上上级代理
            //                 $parent3 = $this->user_model->get_agent_by_id($parent2['spreader_id']);
            //                 // 判断其下级代理个数
            //                 $count3 = count($this->user_model->get_children_agent($parent2['spreader_id'], 1));

            //                 if ($count3 >= 7) { // 享受3级代理收益
            //                     $rate = 0.05;

            //                     // 插入到收益表
            //                     $data = array(
            //                         'a_id' => $adminer['id'],
            //                         'a_account' => $adminer['username'],
            //                         'b_id' => $parent3['id'],
            //                         'b_account' => $parent3['username'],
            //                         'create_time' => time(),
            //                         'type' => 3,
            //                         'money' => $rate * $order['total_fee']
            //                     );

            //                     $this->db->insert('bonus', $data);

            //                     $this->user_model->update_agent_bonus($parent3['id'], $rate * $order['total_fee']);
            //                 }

            //             }
            //         }
            //     }
            // } else {
            //     $this->player_model->set_database($order['game_id']);
            //     $role = $this->player_model->get_role_info($order['role_id']);

            //     // 获取其推荐码
            //     if ($order['game_id'] != 9) {
            //         if ($role && $role['SpreaderID'] > 0) {
            //             // 判断推荐码是否为代理
            //             $agent = $this->user_model->get_one_agent($role['SpreaderID'], $order['game_id']);

            //             if ($agent) {

            //                 $rate = 0.25;

            //                 if ($order['game_id'] == 6) {
            //                     $rate = 0.35;
            //                 } else if ($order['game_id'] == 7) {
            //                     $rate = 0.5;
            //                 } else if ($order['game_id'] == 9) {
            //                     $rate = 0.4;
            //                 } else if ($order['game_id'] == 14) {
            //                     $rate = 0.2;
            //                 }

            //                 // 插入到收益表
            //                 $data = array(
            //                     'a_id' => $role['UserID'],
            //                     'a_account' => $role['Accounts'],
            //                     'b_id' => $agent['id'],
            //                     'b_account' => $agent['username'],
            //                     'create_time' => time(),
            //                     'type' => 4,
            //                     'money' => $rate * $order['total_fee'],
            //                     'order_id' => $order['id']
            //                 );

            //                 $this->db->insert('bonus', $data);

            //                 $this->user_model->update_agent_bonus($agent['id'], $rate * $order['total_fee']);

            //                 // 临海麻将
            //                 if ($order['game_id'] == 6 && $agent['spreader_id'] > 0) {
            //                     // 获取上级代理
            //                     $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);
            //                     $rate = 0.075;
            //                     // 插入到收益表
            //                     $data = array(
            //                         'a_id' => $role['UserID'],
            //                         'a_account' => $role['Accounts'],
            //                         'b_id' => $parent['id'],
            //                         'b_account' => $parent['username'],
            //                         'create_time' => time(),
            //                         'type' => 4,
            //                         'money' => $rate * $order['total_fee']
            //                     );

            //                     $this->db->insert('bonus', $data);

            //                     // 更新收益
            //                     $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

            //                     // 获取上上级代理
            //                     if ($parent['spreader_id'] > 0) {
            //                         $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
            //                         $rate = 0.05;
            //                         // 插入到收益表
            //                         $data = array(
            //                             'a_id' => $role['UserID'],
            //                             'a_account' => $role['Accounts'],
            //                             'b_id' => $parent2['id'],
            //                             'b_account' => $parent2['username'],
            //                             'create_time' => time(),
            //                             'type' => 4,
            //                             'money' => $rate * $order['total_fee']
            //                         );

            //                         $this->db->insert('bonus', $data);

            //                         $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);


            //                         if ($parent2['spreader_id'] > 0) {
            //                             // 获取上上级代理
            //                             $parent3 = $this->user_model->get_agent_by_id($parent2['spreader_id']);

            //                             $rate = 0.025;

            //                             // 插入到收益表
            //                             $data = array(
            //                                 'a_id' => $role['UserID'],
            //                                 'a_account' => $role['Accounts'],
            //                                 'b_id' => $parent3['id'],
            //                                 'b_account' => $parent3['username'],
            //                                 'create_time' => time(),
            //                                 'type' => 4,
            //                                 'money' => $rate * $order['total_fee']
            //                             );

            //                             $this->db->insert('bonus', $data);

            //                             $this->user_model->update_agent_bonus($parent3['id'], $rate * $order['total_fee']);


            //                         }
            //                     }
            //                 }

            //             }
            //         }
            //     }
            //     $this->player_model->update_role_score($order['role_id'], $good['score']);
            // }

            echo "success=Y";
        } else {
            echo "订单状态异常";exit;
        }


    }

    private function _check_sign($data)
    {
        ksort($data);
        reset($data);

        $str = '';

        foreach ($data as $k => $v) {
            if ($k == 'signature') continue;
            if ($v != '') {
                $str .= $k.'='.$v.'&';
            }
        }

        $str .= md5($this->_appSecret);

        $sign = md5($str);

        if(isset($data['signature']) && $sign == $data['signature']) {
            return TRUE;
        }

        return FALSE;
    }

    public function goods()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $role_id = $this->input->get('role_id');

        $channel_id = $this->input->get('channel_id');

        $channel = $this->server_model->get_one_channel($channel_id);

        if(empty($channel)) {
            echo json_encode(array('status' => false, 'msg' => '渠道不存在'));exit;
        }

        $this->player_model->set_database($channel['game_id']);

        $role = $this->player_model->get_role_info($role_id);

        if (!$role) {
            echo json_encode(array('status' => false, 'msg' => '未绑定游戏角色，请下载并登录游戏','is_bind'=>false,'is_agent'=>false));
            exit;
        }

        $adminer = $this->user_model->get_one_agent($role['UserID'],$channel['game_id']);

        $image = ['0'=>'https://file.tuo3.com.cn/a.png','1'=>'https://file.tuo3.com.cn/b.png','2'=>'https://file.tuo3.com.cn/c.png','3'=>'https://file.tuo3.com.cn/d.png','4'=>'https://file.tuo3.com.cn/d.png','5'=>'https://file.tuo3.com.cn/d.png','6'=>'https://file.tuo3.com.cn/d.png','7'=>'https://file.tuo3.com.cn/d.png','8'=>'https://file.tuo3.com.cn/d.png','9'=>'https://file.tuo3.com.cn/d.png','10'=>'https://file.tuo3.com.cn/d.png','11'=>'https://file.tuo3.com.cn/d.png','12'=>'https://file.tuo3.com.cn/d.png','13'=>'https://file.tuo3.com.cn/d.png','14'=>'https://file.tuo3.com.cn/d.png','15'=>'https://file.tuo3.com.cn/d.png'];

        $result = $this->mp_model->get_role_is_first_order($role['UserID'],$channel['game_id']);

        $goods_id = array();
        if (!empty($result)){
            foreach ($result as $key=>$res){
                $goods_id[$key] = $res['good_id'];
            }
        }

        // 是否绑定
        if($role['SpreaderID'] <= 0 && !$adminer) {
            //首充
            if(in_array($channel['game_id'],array(6,7,17))) {
                // 判断是否首充
//                $this->db->join('mp_good', 'mp_good.id=mp_order.good_id');
                $this->db->where('role_id', $role['UserID']);
//                $this->db->where('mp_good.game_id', $channel['game_id']);
                $this->db->where('mp_order.game_id', $channel['game_id']);
                $this->db->where('mp_order.is_first', 1);
                $this->db->where('mp_order.status', 1);
                $query = $this->db->get('mp_order');
                $result = $query->result_array();

                // 获取计费ID
                $this->db->where('type', 1);
                $this->db->order_by('price','ASC');
                if ($result) { // 剔除首充商品
                    $this->db->where('is_first', 0);
                }
                $this->db->where('game_id', $channel['game_id']);
                $query = $this->db->get('mp_good');
                $goods = $query->result_array();
            }else{
                // 获取商品信息
                $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id'],$sort = 'ASC',$goods_id);
            }

            if ($channel['game_id'] != 30){
                foreach ($goods as $key=>$value){
                    $goods[$key]['price'] = ($value['price'] * 1.25);
                }
            }

            if (!empty($goods)){
                foreach ($goods as $key => $value){
                    for ($i=0;$i <= count($image)-1;$i++){
                        if ($i == $key){
                            $value['icon'] = $image[$i];
                        }
                    }
                    $goods[$key] = $value;
                }
            }

            echo json_encode(array('status' => true, 'msg' => '','role' =>array('is_bind'=>false,'is_agent'=>true),'goods'=>$goods,'game_id'=>$channel['game_id']));
            exit;
            // 查询是否绑定代理
            /*            $adminer = $this->user_model->get_one_agent($role['UserID'],$channel['game_id']);
                        //游戏
                        $game = array(9,15);
                        //判断当前游戏id是否存在游戏中
                        if (in_array($channel['game_id'],$game)){
                            //获取全部代理
                            $adminer = $this->user_model->get_all_agency($role['UserID'],$channel['game_id']);
                            //如果是代理 3普通代理 8总代
                            if ($adminer){
                                //如果是总代
                                if ($adminer['group'] == 8){
                                    // 获取商品信息
                                    $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);

                                    echo json_encode(array('status' => true, 'msg' => '', 'is_bind'=>true,'is_agent'=>true,'user' =>array('NickName'=>$role['NickName'],'UserID'=>$role['UserID'],'InsureScore'=>$adminer['score'],'account'=>$adminer['username']),'goods'=>$goods));
                                    exit;
                                    //如果是普通代理
                                }elseif ($adminer['group'] == 3){
                                    //如果绑定了总代理
                                    if (!empty($adminer['spreader_id'])){
                                        echo json_encode(array('status' => false, 'msg' => '代理充值请联系总代;','is_bind'=>false,'is_agent'=>false));
                                        exit();
                                        //如果没有绑定总代理
                                    }else{
                                        // 获取商品信息
                                        $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);

                                        echo json_encode(array('status' => true, 'msg' => '','role' =>array('is_bind'=>false,'is_agent'=>true),'goods'=>$goods));
                                        exit;
                                    }
                                }else{
                                    echo json_encode(array('status' => false, 'msg' => '充值失败,请联系管理员;','role'=> array('is_bind'=>false,'is_agent'=>false)));
                                    exit();
                                }
                            }else{
                                //如果是玩家
                                $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);
                                foreach ($goods as $key=>$value){
                                    $goods[$key]['price'] = ($value['price'] * 1.25);
                                }

                                echo json_encode(array('status' => true, 'msg' => '','role' => array('is_bind'=>false,'is_agent'=>false),'goods'=>$goods));
                                exit();
                            }
                            exit();
                        }

                        //如果已绑定代理
                        if ($adminer) {
                            // 判断有效绑定的玩家个数
                            $this->db->where('agent_id', $adminer['id']);
                            $this->db->where('is_play', 1);
                            $query = $this->db->get('role_bind');
                            $roles = $query->result_array();

                            if(in_array($channel['game_id'],array(6,7)) || count($roles) >= 5) {
                                // 判断是否首充
                                $this->db->join('mp_good','mp_good.id=mp_order.good_id');
                                $this->db->where('role_id', $role['UserID']);
                                $this->db->where('mp_good.game_id',$channel['game_id']);
                                $this->db->where('mp_order.game_id',$channel['game_id']);
                                $this->db->where('mp_good.is_first', 1);
                                $this->db->where('mp_order.status',1);
                                $query = $this->db->get('mp_order');
                                $result = $query->result_array();

                                // 获取计费ID
                                $this->db->where('type', 2);
                                if($result) { // 剔除首充商品
                                    $this->db->where('is_first', 0);
                                }
                                $this->db->where('game_id',$channel['game_id']);
                                $query = $this->db->get('mp_good');
                                $goods = $query->result_array();

                                echo json_encode(array('status' => true, 'msg' => '', 'role' =>array('is_bind'=>false,'is_agent'=>true,),'goods'=>$goods));
                                exit;
                            } else {
                                // 获取计费ID
                                $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);

                                echo json_encode(array('status' => true, 'msg' => '','role' => array('is_bind'=>false,'is_agent'=>false),'goods'=>$goods));
                                exit;
                            }
                        } else {
                            // 获取计费ID
                            $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);
                            foreach ($goods as $key=>$value){
                                $goods[$key]['price'] = ($value['price'] * 1.25);
                            }

                            echo "<pre>";
                            print_r($goods);
                            exit();

                            echo json_encode(array('status' => true, 'msg' => '','role' => array('is_bind'=>false,'is_agent'=>false),'goods'=>$goods));
                            exit;
                        }*/
        }else{
            //首充
            if(in_array($channel['game_id'],array(6,7,17))) {
                // 判断是否首充
                $this->db->join('mp_good', 'mp_good.id=mp_order.good_id');
                $this->db->where('role_id', $role['UserID']);
                $this->db->where('mp_good.game_id', $channel['game_id']);
                $this->db->where('mp_order.game_id', $channel['game_id']);
                $this->db->where('mp_good.is_first', 1);
                $this->db->where('mp_order.status', 1);
                $query = $this->db->get('mp_order');
                $result = $query->result_array();

                // 获取计费ID
                $this->db->where('type', 1);
                $this->db->order_by('price','ASC');
                if ($result) { // 剔除首充商品
                    $this->db->where('is_first', 0);
                }
                $this->db->where('game_id', $channel['game_id']);
                $query = $this->db->get('mp_good');
                $goods = $query->result_array();
            }else{
                // 获取商品信息
                $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id'],$sort = 'ASC',$goods_id);
            }

            if (!empty($goods)){
                foreach ($goods as $key => $value){
                    for ($i=0;$i <= count($image)-1;$i++){
                        if ($i == $key){
                            $value['icon'] = $image[$i];
                        }
                    }
                    $goods[$key] = $value;
                }
            }

            echo json_encode(array('status' => true, 'msg' => '','role' => array('is_bind'=>true,'is_agent'=>true),'goods'=>$goods,'game_id'=>$channel['game_id']));
            exit;
        }
        /*
                // 查询角色房卡
                $score = $this->player_model->get_role_score($role['UserID']);

                // 查询是否绑定代理
                $adminer = $this->user_model->get_one_agent($role['UserID'],$channel['game_id']);

                //游戏
                $game = array(9,15);
                //判断当前游戏id是否存在游戏中
                if (in_array($channel['game_id'],$game)){
                    //获取全部代理
                    $adminer = $this->user_model->get_all_agency($role['UserID'],$channel['game_id']);
                    //如果是代理 3普通代理 8总代
                    if ($adminer){
                        //如果是总代
                        if ($adminer['group'] == 8){
                            // 获取商品信息
                            $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);

                            echo json_encode(array('status' => true, 'msg' => '', 'is_bind'=>true,'is_agent'=>true,'user' =>array('NickName'=>$role['NickName'],'UserID'=>$role['UserID'],'InsureScore'=>$adminer['score'],'account'=>$adminer['username']),'goods'=>$goods));
                            exit;
                            //如果是普通代理
                        }elseif ($adminer['group'] == 3){
                            //如果绑定了总代理
                            if (!empty($adminer['spreader_id'])){
                                echo json_encode(array('status' => false, 'msg' => '代理充值请联系总代;','is_bind'=>false,'is_agent'=>false));
                                exit();
                                //如果没有绑定总代理
                            }else{
                                // 获取商品信息
                                $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);

                                echo json_encode(array('status' => true, 'msg' => '','role' =>array('is_bind'=>true,'is_agent'=>true),'goods'=>$goods));
                                exit;
                            }
                        }else{
                            echo json_encode(array('status' => false, 'msg' => '充值失败,请联系管理员;','role'=> array('is_bind'=>false,'is_agent'=>false)));
                            exit();
                        }
                    }else{
                        //如果是玩家
                        $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);

                        echo json_encode(array('status' => true, 'msg' => '','role' => array('is_bind'=>true,'is_agent'=>false),'goods'=>$goods));
                        exit();
                    }
                    exit();
                }

                //如果已绑定代理
                if ($adminer) {

                    // 判断有效绑定的玩家个数
                    $this->db->where('agent_id', $adminer['id']);
                    $this->db->where('is_play', 1);
                    $query = $this->db->get('role_bind');
                    $roles = $query->result_array();

                    if(!in_array($channel['game_id'],array(6,7)) || count($roles) >= 5) {
                        // 判断是否首充
                        $this->db->join('mp_good','mp_good.id=mp_order.good_id');
                        $this->db->where('role_id', $role['UserID']);
                        $this->db->where('mp_good.game_id',$channel['game_id']);
                        $this->db->where('mp_order.game_id',$channel['game_id']);
                        $this->db->where('mp_good.is_first', 1);
                        $this->db->where('mp_order.status',1);
                        $query = $this->db->get('mp_order');
                        $result = $query->result_array();

                        // 获取计费ID
                        $this->db->where('type', 2);
                        if($result) { // 剔除首充商品
                            $this->db->where('is_first', 0);
                        }
                        $this->db->where('game_id',$channel['game_id']);
                        $query = $this->db->get('mp_good');
                        $goods = $query->result_array();

                        echo json_encode(array('status' => true, 'msg' => '', 'role' =>array('is_bind'=>true,'is_agent'=>true,),'goods'=>$goods));
                        exit;
                    } else {
                        // 获取计费ID
                        $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);

                        echo json_encode(array('status' => true, 'msg' => '','role' => array('is_bind'=>true,'is_agent'=>false),'goods'=>$goods));
                        exit;
                    }

                } else {
                    // 获取计费ID
                    $goods = $this->mp_model->get_mp_good_by_type(1,$channel['game_id']);

                    echo json_encode(array('status' => true, 'msg' => '','role' => array('is_bind'=>true,'is_agent'=>false),'goods'=>$goods));
                    exit;
                }*/

    }

    public function bind()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');

        $ruid = $this->input->post('ruid');
        $uid  =  $this->input->post('role_id');

        $channel_id = $this->input->post('channel_id');

        $channel = $this->server_model->get_one_channel($channel_id);

        if(empty($channel)) {
            echo json_encode(array('status' => false, 'msg' => '渠道不存在'));exit;
        }

        $this->player_model->set_database($channel['game_id']);

        // 松阳麻将暂不开放
        if($channel['game_id'] == 9 || $channel['game_id'] == 15) {
            $this->_response(array('code'=>1,'msg'=>'礼包兑换功能暂未开放'));
        }

        if($ruid == $uid ) {
            $this->_response(array('code'=>1,'msg'=>'推荐人不能为自己'));
        }

        // 查询操作用户是否存在
        $role = $this->player_model->get_role_info($uid);

        if(empty($role)) {
            $this->_response(array('code'=>1,'msg'=>'玩家不存在'));
        }

        // 是否绑定
        if($role['SpreaderID']>0) {
            $this->_response(array('code'=>1,'msg'=>'已绑定，请勿重复绑定'));
        }

        // 查询代理对应的游戏玩家是否存在
        $role2 = $this->player_model->get_role_info($ruid);

        if(empty($role2)) {
            $this->_response(array('code'=>1,'msg'=>'推荐码无效（用户不存在）'));
        }

        // 查询推荐码是否为代理
        $agent = $this->user_model->get_one_agent($ruid,$channel['game_id']);

        if(empty($agent)) {
            $this->_response(array('code'=>1,'msg'=>'推荐码无效（未开通代理资格）'));
        }

        // 查询自己是否为代理
        $agent2 = $this->user_model->get_one_agent($uid,$channel['game_id']);

        if($agent2) {
            // 获取是否有下级代理
            $children = $this->user_model->get_children_agent($agent2['id'],1);

            if(count($children)>0) {
                $this->_response(array('code'=>1,'msg'=>'推荐码无效（已存在下级代理）'));
            }
        }

        // 更新角色
        $this->player_model->update_role_info($uid,array('SpreaderID'=>$ruid));

        $role = $this->player_model->get_role_info($uid);

        if($role['SpreaderID'] == $ruid) {
            $this->player_model->update_role_score($uid,10);

            $score = $this->player_model->get_role_score($uid);

            if($agent2) { //设置为上级代理
                $this->user_model->update_agent_info($agent2['id'],array('spreader_id',$agent['id']));
            }

            $data  = array(
                'role_id'=>$role['UserID'],
                'game_id'=>$channel['game_id'],
                'agent_id'=>$agent['id'],
                'is_play'=>$role['PlayTimeCount']>0?1:0,
                'bind_time'=>time()
            );

            $this->db->insert_role_bind('role_bind',$data);

            $this->_response(array('code'=>0,'msg'=>'绑定成功','ruid'=>(int)$ruid,'insure'=>(int)$score['InsureScore']));
        } else {
            $this->_response(array('code'=>1,'msg'=>'绑定失败，请重试'));
        }

    }

    private function _response($arr)
    {
        echo json_encode($arr);exit;
    }

}