<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>阿里云短信测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"] { width: 100%; padding: 8px; border: 1px solid #ddd; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>阿里云短信服务测试</h1>
        
        <?php if ($_POST): ?>
            <div class="result">
                <?php
                require_once('AliyunSms.class.php');
                
                $config = array(
                    'accessKeyId' => $_POST['accessKeyId'],
                    'accessKeySecret' => $_POST['accessKeySecret'],
                    'signName' => $_POST['signName'],
                    'templateCode' => $_POST['templateCode']
                );
                
                $phone = $_POST['phone'];
                $code = rand(1000, 9999);
                
                echo "<div class='info'>";
                echo "<strong>测试参数：</strong><br>";
                echo "手机号: $phone<br>";
                echo "签名: " . $config['signName'] . "<br>";
                echo "模板: " . $config['templateCode'] . "<br>";
                echo "验证码: $code<br>";
                echo "</div>";
                
                $sms = new AliyunSms($config);
                $result = $sms->templateSMS('', $phone, $config['templateCode'], $code);
                $response = json_decode($result);
                
                echo "<br><strong>发送结果：</strong><br>";
                echo "原始响应: " . htmlspecialchars($result) . "<br><br>";
                
                if ($response && $response->resp->respCode == "000000") {
                    echo "<div class='success'>";
                    echo "✅ <strong>发送成功！</strong><br>";
                    echo "请检查手机 $phone 是否收到验证码: $code";
                    echo "</div>";
                } else {
                    echo "<div class='error'>";
                    echo "❌ <strong>发送失败</strong><br>";
                    if ($response) {
                        echo "错误码: " . $response->resp->respCode . "<br>";
                        echo "错误信息: " . $response->resp->respMsg . "<br>";
                    }
                    echo "<br><strong>故障排查：</strong><br>";
                    echo "1. 确认AccessKey信息正确<br>";
                    echo "2. 确认签名和模板已审核通过<br>";
                    echo "3. 确认账户余额充足<br>";
                    echo "4. 确认手机号格式正确<br>";
                    echo "</div>";
                }
                ?>
            </div>
        <?php endif; ?>
        
        <form method="post">
            <div class="form-group">
                <label>AccessKey ID:</label>
                <input type="text" name="accessKeyId" value="<?= $_POST['accessKeyId'] ?? '' ?>" required>
            </div>
            
            <div class="form-group">
                <label>AccessKey Secret:</label>
                <input type="text" name="accessKeySecret" value="<?= $_POST['accessKeySecret'] ?? '' ?>" required>
            </div>
            
            <div class="form-group">
                <label>短信签名:</label>
                <input type="text" name="signName" value="<?= $_POST['signName'] ?? '' ?>" required>
            </div>
            
            <div class="form-group">
                <label>模板CODE:</label>
                <input type="text" name="templateCode" value="<?= $_POST['templateCode'] ?? '' ?>" required>
            </div>
            
            <div class="form-group">
                <label>测试手机号:</label>
                <input type="text" name="phone" value="<?= $_POST['phone'] ?? '' ?>" required>
            </div>
            
            <button type="submit">发送测试短信</button>
        </form>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <h3>使用说明：</h3>
            <ol>
                <li>在阿里云控制台获取AccessKey ID和Secret</li>
                <li>申请并通过审核的短信签名</li>
                <li>申请并通过审核的短信模板（需包含${code}变量）</li>
                <li>填写上述信息和测试手机号</li>
                <li>点击发送测试短信</li>
            </ol>
        </div>
    </div>
</body>
</html>
