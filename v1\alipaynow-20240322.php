<?php

/**
 * Created by Php<PERSON>torm.
 * User: Jason.z<http://www.jason-z.com>
 * Date: 2017/6/7
 * Time: 下午2:42
 */

defined('BASEPATH') or exit ('No direct script access allowed');

require_once APPPATH . 'libraries/alipay/aop/AopClient.php';
require_once APPPATH . 'libraries/alipay/aop/request/AlipayTradeWapPayRequest.php';
require_once APPPATH . 'libraries/alipay/aop/request/AlipayTradeQueryRequest.php';

class Alipaynow extends CI_Controller
{
    private $appId = '2021004135693334';
    private $rsaPrivateKey = 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCje5yRNiiH2DIJKWuZ2wj7qiP7vkNmX1uQsX7OyEb5mHEi533/p2bo6LnPBn+tlZlIIIKRqbqXl6FtBYdhCaHOIWrFN7fctRNWJEgtYgzQ1ujgBL5DCs0By9uX0IQ8r+CpRtJaLt6PmKl1PqVgWyNhUjbLb14yS8YyKNeGBHb8zPWpI9L9K/EdIV6cWaPnpp/OGePNVoGBzSsW1Ecw2WCjiKERSx+ddR72NMjEPrXCo5UtTcOo5dp2CHfEh7q3KEUmxZDxvQSBjDl6Q3zarrEpnwm/Tr2wAOsVXS3B93bn4AEQURhFCOrycl72U6wHrbWsqgfuoUqUvd6eq0IT5pU/AgMBAAECggEALJHHpoMYW6iiQ4MqVaC0ez/wXXKKl39JzFHHm4JYbzWTKovn+undkYPZHErORjmxt0s9rY6TUz7AgKNwPBFPLo5HuIqvMpmfMu3jS5QeoFTm2iQQ5uAr8eBs9p+fXZgpbJzlPjbm8EkTceZpWbKfG6o5TA8JpDaqSwR9dYkEE3kwssGd+UsN8vzmzbsPl3mXV2pYzK64bhIVbq5Y9Hy5UUtQ8wAw3lwQevKGlorubU8XOqaglnDamBjsWm0Z1ERKTcKE2bStulhEwf6GrmxG5ex8L7PauXEHzrv7FN/w9AghscbWiT5utnkaAyWbrSnQ5VANMrMOWc+ddXf9P+dDcQKBgQDqMbsRoGGQ1idgaXJDwz83ghb3c0BJEPSErgHkjjrgvqNUXX5actFhRVW/bO4iX6N12b7mw5BYcyskoCyN/pvkz9UAnKrXC1hK+ouOpr0amrvy3ahsrsQ6+yn9dY2vP5CAJuVuY1wet8uL+AcJSf2YePR0cxdh3xSYZwl+IHQK9wKBgQCytGY8yTJLVDfDrIIdQ+xRQdt16lz8PLadFdKE+2aNGreeQNf2WNWXOEWIL8pZwXVKL3e4NwbGRVqTEabHxjOIs3zFDnRgSHtiZSLD745rNnotcgwu21QVBLmlZ2+HJxzqPSkelSnW8qfZTvjRo9yWbhyAdVwSnwX5IYgURpmt+QKBgQDV+qpZ6Jg8pe9EgBQPJg5UwWAKqBtMp1lDAVppYisZvhWtt98C9XWp0pgOV5EdhxTrURDD+Fui9VeuF0ueUcxOvDZ6YKsX4R3DJDjvFhoifM7NkQmag/R3j5VGKGpUfVeiP+fRfKdmybgJI44KRriW36072Qy8N/+xYDOkMjLU8QKBgEt6JjRGRhfK2kKq+cmiho1LY4XKFrvyaK6wKNpB1G6Eekk82foSSyQzgCqlBLIkI4XRxp7nntTfNgIn0mlOLTXvmSMhl8WS14oXbTeUVnKx76iCIuMnf7vg4wgZR+Gtg2jrrBJere07T+lYkX751pEysJcQ+nJi+ALhRrhJmV8BAoGALXGQR/2df0CzUH1t67DUmJX+8I6JA2S3TpVzafU2JUtWQ7PYHqtyu1gl0gP/2urMB5jWFkApM6DpyLvXNZmX4xbesuNXpXhdyoHbVaEPgfuj+OS2D+ZKlumgqJ9sAWiBgIUdKj5t4SXBuncaMbY4WLI9u5Sg5/AOYwHBNDwOZIE='; //私钥
    private $alipayrsaPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwBziaNpDbhCeQdY1iOCT4UI6oEb+79yJCEMIXyXLetdagCV6MObZ3znt1SzbNBwCJp8ySZUNH1rwfccKHzRHCtn91kTAfYPMe+MFzgTSDoRSIZcNfJNvz+cXoQ9eMPv1Ozp15eSBYqVUOyFF8i4RX19FLzjb5oPm3lG9l+ojBA3LLaK9raR6sYiDZDsJ5w/bQjtwQYGzJo1Wl4Fw8/C9gwj09yvOXykPMJ0P2MPl08EpA0dpiH424K6qVR4qzYsn9SrJ4YiFXGLo0e5Dp+uFjNMmcBmYK3kjr1YJGWdwMV3TgbAck9jP6A+IZMPL3dBcdQ0ZzqoDRdcf/iTTAHMynQIDAQAB'; //公钥
    private $gatewayUrl = 'https://openapi.alipay.com/gateway.do'; //网关地址
    private $returnUrl = 'https://lhmj.tuo3.com.cn/admin/api/v1/Alipaynow/returnPay'; //同步跳转地址
    private $notifyUrl = 'https://lhmj.tuo3.com.cn/admin/api/v1/Alipaynow/notifyPay'; //异步通知地址
    private $quitUrl = ''; //支付失败退出地址
    private $version = '1.0';
    private $signType = 'RSA2';
    private $charset = 'utf-8';
    private $format = 'json';

    function __construct()
    {
        parent::__construct();
        $this->load->model('mp_model');
        $this->load->model('server_model');
        $this->load->model('player_model');
        $this->load->model('user_model');
    }

    public function index(){
        $this->load->view('alipay/index');
    }
      
    public function pay(){
        $good_id = $this->input->get('good_id');
        $role_id = $this->input->get('user_id');
        $game_id = $this->input->get('game_id');
        $this->player_model->set_database($game_id);

        // 查询角色是否存在
        $role = $this->player_model->get_role_info($role_id);

        if (empty ($role)) {
            echo json_encode(array('status' => false, 'msg' => '角色不存在'));
            exit;
        }

        // 获取商品信息
        $good = $this->mp_model->get_mp_good_by_id($good_id);

        if (empty ($good)) {
            echo json_encode(array('status' => false, 'msg' => '请选择充值商品'));
            exit;
        }
        //获取用户微信信息
        $mp_user = $this->mp_model->get_mp_user_by_roleid($role_id, $game_id);

        $agent = $this->user_model->get_one_agent($role_id, $game_id);
        
        $order_no = date("YmdHis") . random_string('nozero', 3);

        $price =  $good['price'];
        // 创建订单
        $data = array(
            'user_id' => $mp_user['user_id'],
            'role_id' => $role_id,
            'agent_id' => $agent ? $agent['id'] : 0,
            'good_id' => $good_id,
            'game_id' =>$game_id,
            'order_no' => $order_no,
            'is_first' => $good['is_first'],
            'is_agent' => ($good['type'] - 1),
            'total_fee' => $price,
            'is_mall' => 1,
            'kind_id' => $role['CommonKindID'],
            'create_time' => time()
        );

        $this->db->insert('mp_order', $data);
        $aop = new AopClient();
        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = $this->appId;
        $aop->rsaPrivateKey = $this->rsaPrivateKey;
        $aop->alipayrsaPublicKey = $this->alipayrsaPublicKey;
        $aop->apiVersion = '1.0';
        $aop->signType = 'RSA2';
        $aop->postCharset = 'UTF-8';
        $aop->format = 'json';
        /******必传参数******/
        $object = new stdClass();
        //商户订单号，商家自定义，保持唯一性
        $object->out_trade_no = $order_no;        
        //支付金额，最小值0.01元
        // $object->total_amount = $good['price'];
        $object->total_amount = $price;
        //订单标题，不可使用特殊符号
        $object->subject = $good['name'];
        /******可选参数******/
        //手机网站支付默认传值QUICK_WAP_WAY
        $object->product_code = 'QUICK_WAP_WAY';
        $object->time_expire = date("Y-m-d H:i:s", strtotime("+1 hours"));
        $json = json_encode($object);
        $request = new AlipayTradeWapPayRequest();
        //异步接收地址，仅支持http/https，公网可访问
        $request->setNotifyUrl($this->notifyUrl);
        //同步跳转地址，仅支持http/https
        $request->setReturnUrl($this->returnUrl);
        $request->setBizContent($json);
        $pageRedirectionData = $aop->pageExecute($request, "POST");
        echo json_encode(array('status' => true, 'msg' => '', 'data'=>$pageRedirectionData));
        exit; 
    }
    public function returnPay()
    {
        $out_trade_no = $this->input->get('out_trade_no');
        $trade_no = $this->input->get('trade_no');        
        log_message("error", "支付宝-现在支付页面返回");
        log_message("error", print_r($this->input->get(),true));
        $this->payCheck($out_trade_no,$trade_no);
        exit;
    }


    public function notifyPay()
    {
        $content = file_get_contents('php://input');
        parse_str($content,$result);
        $out_trade_no = $result['out_trade_no'];
        $trade_no = $result['trade_no'];        
        log_message("error", "支付宝-现在支付回调");
        log_message("error", print_r($result,true));
        $this->payCheck($out_trade_no,$trade_no);
        exit;
    }
    private function payCheck($out_trade_no,$trade_no){
        // 查找订单
        $this->db->where('order_no', $out_trade_no);
        $query = $this->db->get('mp_order');
        $order = $query->row_array();
        if(empty($order)){
            log_message("error", "支付宝-订单未找到");
            echo "订单未找到";
            exit;
        }
        if ($order['status'] == 1) {
            log_message("error", "支付宝-交易已完成");
            echo "交易已完成,请退出游戏重新登陆";
            exit;
        }
        log_message('error', '订单信息');
        log_message('error', print_r($order,true));       
    
        $aop = new AopClient();

        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = $this->appId;
        $aop->rsaPrivateKey = $this->rsaPrivateKey;
        $aop->alipayrsaPublicKey = $this->alipayrsaPublicKey;
        $aop->apiVersion = '1.0';
        $aop->signType = 'RSA2';
        $aop->postCharset = 'UTF-8';
        $aop->format = 'json';
       
        $object = new stdClass();
        $object->out_trade_no = $out_trade_no;
        //$object->trade_no = '2014112611001004680073956707';
        $json = json_encode($object);
        $request = new AlipayTradeQueryRequest();
        $request->setBizContent($json);
        $result = $aop->execute($request); 
        if(empty($result->alipay_trade_query_response)){
            log_message('error', '获取支付宝支付信息失败');
            exit;
        }
        $result = $result->alipay_trade_query_response;
        // dump($paymentInfo);
        if ($result->code === "10000" && $result->trade_status == "TRADE_SUCCESS") {
            $this->db->where('id', $order['id']);
            $this->db->update('mp_order', array('transaction_id' => $trade_no, 'pay_time' => time(), 'status' => 1));

            $this->player_model->set_database($order['game_id']);

            // 获取商品信息
            $good = $this->mp_model->get_mp_good_by_id($order['good_id']);
            $role = $this->player_model->get_role_info($order['role_id']);

            //添加房卡
            $this->player_model->update_role_score($order['role_id'], $good['score']);
            $adminer = array();
            if ($order['game_id'] == 30) {
                // 查询是否为代理
                $adminer = $this->user_model->get_one_agent($order['role_id'], $order['game_id']);
            }

            // 获取推荐代理
            if ($role && $role['SpreaderID'] > 0) {

                $agent = $this->user_model->get_one_agent($role['SpreaderID'], $order['game_id']);

                if ($agent) {

                    $rate = 0.4;
                    if ($order['game_id'] == 35) {
                        $rate = 0.45;
                    }
                    if ($order['game_id'] == 32) {
                        $rate = 0.5;
                    }
                    if ($order['game_id'] == 30) {
                        $rate = 0.376;
                    }

                    // 插入到收益表
                    $data = array(
                        'a_id' => $role['UserID'],
                        'a_account' => $role['Accounts'],
                        'b_id' => $agent['id'],
                        'b_account' => $agent['username'],
                        'create_time' => time(),
                        'type' => 4,
                        'money' => $rate * $order['total_fee'],
                        'district_id' => empty ($agent['district_id']) ? 0 : $agent['district_id']
                    );

                    $this->db->insert('bonus', $data);

                    $this->user_model->update_agent_bonus($agent['id'], $rate * $order['total_fee']);

                    // 获取上级代理
                    $parent = $this->user_model->get_agent_by_id($agent['spreader_id']);

                    // 判断其下级代理个数
                    $count = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                    $rate = 0.06;

                    if ($order['game_id'] == 30) {
                        $rate = 0.047;
                    }

                    // 插入到收益表
                    $data = array(
                        'a_id' => $agent['id'],
                        'a_account' => $agent['username'],
                        'b_id' => $parent['id'],
                        'b_account' => $parent['username'],
                        'create_time' => time(),
                        'type' => 1,
                        'money' => $rate * $order['total_fee'],
                        'district_id' => empty ($agent['district_id']) ? 0 : $agent['district_id']
                    );

                    $this->db->insert('bonus', $data);

                    $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

                    if ($parent['spreader_id'] > 0) {
                        $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);
                        // 判断其下级代理个数
                        $count2 = count($this->user_model->get_children_agent($parent['spreader_id'], 1));

                        $rate = 0.04;

                        if ($order['game_id'] == 30) {
                            $rate = 0.0188;
                        }

                        // 插入到收益表
                        $data = array(
                            'a_id' => $agent['id'],
                            'a_account' => $agent['username'],
                            'b_id' => $parent2['id'],
                            'b_account' => $parent2['username'],
                            'create_time' => time(),
                            'type' => 2,
                            'money' => $rate * $order['total_fee'],
                            'district_id' => empty ($agent['district_id']) ? 0 : $agent['district_id']
                        );

                        $this->db->insert('bonus', $data);

                        $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);
                    }
                }                
            } else if ($adminer) {
                $parent = $this->user_model->get_agent_by_id($adminer['spreader_id']);

                if (in_array($order['game_id'], array(30))) {
                    $rate = 0.376;
                }

                // 插入到收益表
                $data = array(
                    'a_id' => $adminer['id'],
                    'a_account' => $adminer['username'],
                    'b_id' => $parent['id'],
                    'b_account' => $parent['username'],
                    'create_time' => time(),
                    'type' => 1,
                    'money' => $rate * $order['total_fee'],
                    'district_id' => empty ($adminer['district_id']) ? 0 : $adminer['district_id']
                );

                $this->db->insert('bonus', $data);

                // 更新收益
                $this->user_model->update_agent_bonus($parent['id'], $rate * $order['total_fee']);

                // 获取上上级代理
                if ($parent['spreader_id'] > 0) {
                    $parent2 = $this->user_model->get_agent_by_id($parent['spreader_id']);

                    if (in_array($order['game_id'], array(30))) {
                        $rate = 0.047;
                    }

                    // 插入到收益表
                    $data = array(
                        'a_id' => $adminer['id'],
                        'a_account' => $adminer['username'],
                        'b_id' => $parent2['id'],
                        'b_account' => $parent2['username'],
                        'create_time' => time(),
                        'type' => 2,
                        'money' => $rate * $order['total_fee'],
                        'district_id' => empty ($adminer['district_id']) ? 0 : $adminer['district_id']
                    );

                    $this->db->insert('bonus', $data);

                    $this->user_model->update_agent_bonus($parent2['id'], $rate * $order['total_fee']);

                    if ($parent2['spreader_id'] > 0) {
                        // 获取上上级代理
                        $parent3 = $this->user_model->get_agent_by_id($parent2['spreader_id']);

                        if (in_array($order['game_id'], array(30))) {
                            $rate = 0.0188;
                        }

                        // 插入到收益表
                        $data = array(
                            'a_id' => $adminer['id'],
                            'a_account' => $adminer['username'],
                            'b_id' => $parent3['id'],
                            'b_account' => $parent3['username'],
                            'create_time' => time(),
                            'type' => 3,
                            'money' => $rate * $order['total_fee'],
                            'district_id' => empty ($adminer['district_id']) ? 0 : $adminer['district_id']
                        );

                        $this->db->insert('bonus', $data);

                        $this->user_model->update_agent_bonus($parent3['id'], $rate * $order['total_fee']);
                    }
                }
            }
            log_message("error", "支付宝-支付成功");
            echo "支付成功";
            exit;
        } else {
            log_message("error", "支付宝-交易已完成");
            echo "订单状态异常";
            exit;
        }
    }



    public function goods()
    {
        header("Access-Control-Allow-Origin:*");
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: *');
       
        $role_id = $this->input->get('user_id');

        $game_id = $this->input->get('game_id');

        $this->player_model->set_database($game_id);

        $role = $this->player_model->get_role_info($role_id);

        if (!$role) {
            echo json_encode(array('status' => false, 'msg' => '未绑定游戏角色，请下载并登录游戏', 'is_bind' => false, 'is_agent' => false));
            exit;
        }

        $adminer = $this->user_model->get_one_agent($role['UserID'], $game_id);

        $image = ['0' => 'https://file.tuo3.com.cn/a.png', '1' => 'https://file.tuo3.com.cn/b.png', '2' => 'https://file.tuo3.com.cn/c.png', '3' => 'https://file.tuo3.com.cn/d.png', '4' => 'https://file.tuo3.com.cn/d.png', '5' => 'https://file.tuo3.com.cn/d.png', '6' => 'https://file.tuo3.com.cn/d.png', '7' => 'https://file.tuo3.com.cn/d.png', '8' => 'https://file.tuo3.com.cn/d.png', '9' => 'https://file.tuo3.com.cn/d.png', '10' => 'https://file.tuo3.com.cn/d.png', '11' => 'https://file.tuo3.com.cn/d.png', '12' => 'https://file.tuo3.com.cn/d.png', '13' => 'https://file.tuo3.com.cn/d.png', '14' => 'https://file.tuo3.com.cn/d.png', '15' => 'https://file.tuo3.com.cn/d.png'];

        $result = $this->mp_model->get_role_is_first_order($role['UserID'], $game_id);

        $goods_id = array();
        if (!empty ($result)) {
            foreach ($result as $key => $res) {
                $goods_id[$key] = $res['good_id'];
            }
        }

        // 是否绑定
        if ($role['SpreaderID'] <= 0 && !$adminer) {
            //首充
            if (in_array($game_id, array(6, 7, 17))) {
                // 判断是否首充
                //                $this->db->join('mp_good', 'mp_good.id=mp_order.good_id');
                $this->db->where('role_id', $role['UserID']);
                //                $this->db->where('mp_good.game_id', $game_id);
                $this->db->where('mp_order.game_id', $game_id);
                $this->db->where('mp_order.is_first', 1);
                $this->db->where('mp_order.status', 1);
                $query = $this->db->get('mp_order');
                $result = $query->result_array();

                // 获取计费ID
                $this->db->where('type', 1);
                $this->db->order_by('price', 'ASC');
                if ($result) { // 剔除首充商品
                    $this->db->where('is_first', 0);
                }
                $this->db->where('game_id', $game_id);
                $query = $this->db->get('mp_good');
                $goods = $query->result_array();
            } else {
                // 获取商品信息
                $goods = $this->mp_model->get_mp_good_by_type(1, $game_id, $sort = 'ASC', $goods_id);
            }

            if ($game_id != 30) {
                foreach ($goods as $key => $value) {
                    $goods[$key]['price'] = ($value['price'] * 1.25);
                }
            }

            if (!empty ($goods)) {
                foreach ($goods as $key => $value) {
                    for ($i = 0; $i <= count($image) - 1; $i++) {
                        if ($i == $key) {
                            $value['icon'] = $image[$i];
                        }
                    }
                    $goods[$key] = $value;
                }
            }

            echo json_encode(array('status' => true, 'msg' => '', 'role' => array('is_bind' => false, 'is_agent' => true), 'goods' => $goods, 'game_id' => $game_id));
            exit;           
        } else {
            //首充
            if (in_array($game_id, array(6, 7, 17))) {
                // 判断是否首充
                $this->db->join('mp_good', 'mp_good.id=mp_order.good_id');
                $this->db->where('role_id', $role['UserID']);
                $this->db->where('mp_good.game_id', $game_id);
                $this->db->where('mp_order.game_id', $game_id);
                $this->db->where('mp_good.is_first', 1);
                $this->db->where('mp_order.status', 1);
                $query = $this->db->get('mp_order');
                $result = $query->result_array();

                // 获取计费ID
                $this->db->where('type', 1);
                $this->db->order_by('price', 'ASC');
                if ($result) { // 剔除首充商品
                    $this->db->where('is_first', 0);
                }
                $this->db->where('game_id', $game_id);
                $query = $this->db->get('mp_good');
                $goods = $query->result_array();
            } else {
                // 获取商品信息
                $goods = $this->mp_model->get_mp_good_by_type(1, $game_id, $sort = 'ASC', $goods_id);
            }

            if (!empty ($goods)) {
                foreach ($goods as $key => $value) {
                    for ($i = 0; $i <= count($image) - 1; $i++) {
                        if ($i == $key) {
                            $value['icon'] = $image[$i];
                        }
                    }
                    $goods[$key] = $value;
                }
            }

            echo json_encode(array('status' => true, 'msg' => '', 'role' => array('is_bind' => true, 'is_agent' => true), 'goods' => $goods, 'game_id' => $game_id));
            exit;
        }        
    }

    private function getOptions()
    {
        $options = new Config();
        $options->protocol = 'https';

        $options->gatewayHost = 'openapi.alipay.com';

        $options->signType = 'RSA2';

        $options->appId = '2021004135693334';

        // 为避免私钥随源码泄露，推荐从文件中读取私钥字符串而不是写入源码中

        $options->merchantPrivateKey = $this->rsaPrivateKey;

        // $options->alipayCertPath = '';

        // $options->alipayRootCertPath = '';

        // $options->merchantCertPath = '<-- 请填写您的应用公钥证书文件路径，例如：/foo/appCertPublicKey_2019051064521003.crt -->';
        // $options->merchantCertPath ='';

        //注：如果采用非证书模式，则无需赋值上面的三个证书路径，改为赋值如下的支付宝公钥字符串即可

        // $options->alipayPublicKey = '<-- 请填写您的支付宝公钥，例如：MIIBIjANBg... -->';
        $options->alipayPublicKey = $this->alipayrsaPublicKey;

        //可设置异步通知接收服务地址（可选）

        //如果需要使用文件上传接口，请不要设置该参数

        $options->notifyUrl = $this->notifyUrl;

        //可设置AES密钥，调用AES加解密相关接口时需要（可选）

        $options->encryptKey = " rQKvTPIsKLmZ6RWRSOGDCA==";

        return $options;
    }
    private function _response($arr)
    {
        echo json_encode($arr);
        exit;
    }
}
