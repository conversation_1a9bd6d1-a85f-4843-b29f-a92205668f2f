<?php
/**
 * Created by PhpStorm.
 * User: tuo3
 * Date: 2017/2/8
 * Time: 上午11:48
 */

require(APPPATH.'/libraries/REST_Controller.php');

class Room extends REST_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('api_model', '', TRUE);
        $this->load->model('server_model');
    }
    
    // 创建房间前的检查
    public function check_post()
    {
        $uid = $this->input->post('uid');//获取用户id
        $kind_id = $this->input->post('kind');//获取游戏玩法id
        $room_id = $this->input->post('room_id');//获取游戏玩法id

        $game = $this->input->post('game');//获取游戏玩法id

        $c_version =  $this->input->post('c_version');
        $res_version =  $this->input->post('res_version');
        $version_no =  $this->input->post('version_no');

        if(!$kind_id&&$room_id) {
            $this->server_model->set_database($this->_channel['game_id']);
            $room  = $this->server_model->get_room_info($room_id);

            if($room) {
                $kind_id = $room['KindID'];
            }
        }

        $room_link = "";

        if(in_array($this->_channel['game_id'],array(20,30,32,36,37,54))) {
            $room_link = "https://lhmj.tuo3.com.cn/admin/down/room2";
        }

        if($c_version < $this->_channel['client_version']) {
            if(in_array($this->_channel['channel_id'],array(60010001,15010001))) {
                $this->response(array('code' => 0, 'msg' => '客户端版本过低，请重启游戏或手机更新','is_restart'=>1,'room_link'=>$room_link));
            } else {
                $this->response(array('code' => 1, 'msg' => '客户端版本过低，请重启游戏或手机更新','room_link'=>$room_link));
            }
        }

        if($c_version == $this->_channel['client_version']) {
            if($res_version < $this->_channel['hall_res_version'])
            {
                if(in_array($this->_channel['channel_id'],array(60010001,15010001))) {
                    $this->response(array('code' => 0, 'msg' => '客户端版本过低，请重启游戏或手机更新','is_restart'=>1,'room_link'=>$room_link));
                } else {
                    $this->response(array('code' => 1, 'msg' => '客户端版本过低，请重启游戏或手机更新','room_link'=>$room_link));
                }
            }

            if($res_version == $this->_channel['hall_res_version']) {
                $type = $this->server_model->get_type_by_module($game);

                if ($type) {

                    $game_version = $this->server_model->get_channel_version($this->_channel['channel_id'], $type['type_id'], 0);

                    if ($game_version) {

                        if ($version_no) {
                            $arr = explode('.', $version_no);

                            if (isset($arr[2])) {
                                if ($arr[2] < $game_version['version_no']) {
                                    if(in_array($this->_channel['channel_id'],array(60010001,15010001))) {
                                        $this->response(array('code' => 0, 'msg' => '客户端版本过低，请重启游戏或手机更新','is_restart'=>1,'room_link'=>$room_link));
                                    } else {
                                        $this->response(array('code' => 1, 'msg' => '客户端版本过低，请重启游戏或手机更新','room_link'=>$room_link));
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }

        $list_status = 1;//白名单用户状态 0禁用 1未禁用

//        $server = $this->server_model->get_one_server_config(
//            array(
//                'channel_id'=>$this->_channel['channel_id']
//            )
//        );

        $whitelist = $this->server_model->get_user_status($this->_channel['game_id'], $list_status,$uid);//获取用户白名单信息
        $game_kind = $this->server_model->get_game_kind($this->_channel['game_id'], $kind_id);//获取当前游戏玩法状态



        $ip_arr = explode(',',$this->_channel['ip_whitelist']);
//        var_dump($ip_arr);
//        var_dump(in_array($this->getIp(),$ip_arr));
        if($ip_arr && in_array($this->getIp(),$ip_arr)) {
            $this->response(array('code' => 0, 'msg' => '','room_link'=>$room_link));
        }

        //判断玩法是否在维护
        if ($game_kind) {

            if($game_kind['maintenance_begin_time'] <= time()&&$game_kind['maintenance_end_time'] >= time()) {
                //判断用户是否在白名单内
                if (empty($whitelist)) {
                    $this->response(array('code' => 1, 'msg' => $game_kind['maintenance_content'],'room_link'=>$room_link));
                }
            }
        }
        $this->response(array('code' => 0, 'msg' => '','room_link'=>$room_link));
    }

    public function getIp()
    {

        if(!empty($_SERVER["HTTP_CLIENT_IP"]))
        {
            $cip = $_SERVER["HTTP_CLIENT_IP"];
        }
        else if(!empty($_SERVER["HTTP_X_FORWARDED_FOR"]))
        {
            $cip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        }
        else if(!empty($_SERVER["REMOTE_ADDR"]))
        {
            $cip = $_SERVER["REMOTE_ADDR"];
        }
        else
        {
            $cip = '';
        }
        preg_match("/[\d\.]{7,15}/", $cip, $cips);
        $cip = isset($cips[0]) ? $cips[0] : 'unknown';
        unset($cips);

        return $cip;
    }

}