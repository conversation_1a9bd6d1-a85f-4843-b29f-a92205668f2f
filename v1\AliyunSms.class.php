<?php
/**
 * 阿里云短信服务类
 * 用于替换云之讯短信服务
 */
class AliyunSms
{
    private $accessKeyId;
    private $accessKeySecret;
    private $signName;
    private $endpoint = 'https://dysmsapi.aliyuncs.com';
    
    public function __construct($options = array())
    {
        $this->accessKeyId = isset($options['accessKeyId']) ? $options['accessKeyId'] : '';
        $this->accessKeySecret = isset($options['accessKeySecret']) ? $options['accessKeySecret'] : '';
        $this->signName = isset($options['signName']) ? $options['signName'] : '';
    }
    
    /**
     * 发送短信验证码
     * @param string $phoneNumber 手机号码
     * @param string $templateCode 短信模板CODE
     * @param string $templateParam 短信模板变量对应的实际值，JSON格式
     * @return string JSON格式的响应结果
     */
    public function sendSms($phoneNumber, $templateCode, $templateParam = '')
    {
        $params = array(
            'PhoneNumbers' => $phoneNumber,
            'SignName' => $this->signName,
            'TemplateCode' => $templateCode,
            'TemplateParam' => $templateParam,
            'RegionId' => 'cn-hangzhou',
            'Action' => 'SendSms',
            'Version' => '2017-05-25',
            'AccessKeyId' => $this->accessKeyId,
            'Format' => 'JSON',
            'SignatureMethod' => 'HMAC-SHA1',
            'SignatureVersion' => '1.0',
            'SignatureNonce' => uniqid(),
            'Timestamp' => gmdate('Y-m-d\TH:i:s\Z')
        );
        
        // 计算签名
        $signature = $this->computeSignature($params, $this->accessKeySecret);
        $params['Signature'] = $signature;
        
        // 发送请求
        $response = $this->sendRequest($params);
        
        return $response;
    }
    
    /**
     * 计算签名
     */
    private function computeSignature($parameters, $accessKeySecret)
    {
        ksort($parameters);
        $canonicalizedQueryString = '';
        foreach ($parameters as $key => $value) {
            $canonicalizedQueryString .= '&' . $this->percentEncode($key) . '=' . $this->percentEncode($value);
        }
        
        $stringToSign = 'POST&%2F&' . $this->percentEncode(substr($canonicalizedQueryString, 1));
        
        return base64_encode(hash_hmac('sha1', $stringToSign, $accessKeySecret . '&', true));
    }
    
    /**
     * URL编码
     */
    private function percentEncode($str)
    {
        $res = urlencode($str);
        $res = preg_replace('/\+/', '%20', $res);
        $res = preg_replace('/\*/', '%2A', $res);
        $res = preg_replace('/%7E/', '~', $res);
        return $res;
    }
    
    /**
     * 发送HTTP请求
     */
    private function sendRequest($params)
    {
        $postData = http_build_query($params);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->endpoint);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/x-www-form-urlencoded'
        ));
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return json_encode(array('Code' => 'RequestFailed', 'Message' => $error));
        }
        
        curl_close($ch);
        
        if ($httpCode != 200) {
            return json_encode(array('Code' => 'HttpError', 'Message' => 'HTTP Error: ' . $httpCode));
        }
        
        return $response;
    }
    
    /**
     * 兼容原有的templateSMS方法
     * @param string $appId 应用ID（阿里云中不需要，保留参数兼容性）
     * @param string $phone 手机号码
     * @param string $templateId 模板ID
     * @param string $code 验证码
     * @return string JSON格式的响应结果
     */
    public function templateSMS($appId, $phone, $templateId, $code)
    {
        $templateParam = json_encode(array('code' => $code));
        $response = $this->sendSms($phone, $templateId, $templateParam);
        
        // 转换响应格式以兼容原有代码
        $result = json_decode($response, true);
        
        if (isset($result['Code']) && $result['Code'] == 'OK') {
            // 成功响应，转换为云之讯格式
            return json_encode(array(
                'resp' => array(
                    'respCode' => '000000',
                    'respMsg' => 'Success'
                )
            ));
        } else {
            // 失败响应，转换为云之讯格式
            $errorMsg = isset($result['Message']) ? $result['Message'] : 'Unknown error';
            return json_encode(array(
                'resp' => array(
                    'respCode' => '999999',
                    'respMsg' => $errorMsg
                )
            ));
        }
    }
}
?>
